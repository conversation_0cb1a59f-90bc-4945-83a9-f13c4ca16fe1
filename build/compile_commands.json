[{"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/DataGenerator.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/DataGenerator.cpp"}, {"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/HungarianMatcher.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/HungarianMatcher.cpp"}, {"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/KalmFilter3D.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/KalmFilter3D.cpp"}, {"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/PointTracker.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/PointTracker.cpp"}, {"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/Utils.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/Utils.cpp"}, {"directory": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build", "command": "/usr/bin/c++  -I/home/<USER>/My_Project/MSHNet_TensorRT_Infer/include -I/usr/include/eigen3 -g -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/main.cpp", "file": "/home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/main.cpp"}]