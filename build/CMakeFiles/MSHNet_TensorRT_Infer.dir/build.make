# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Infer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build

# Include any dependencies generated for this target.
include CMakeFiles/MSHNet_TensorRT_Infer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/MSHNet_TensorRT_Infer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o: ../src/DataGenerator.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/DataGenerator.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/DataGenerator.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/DataGenerator.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.s

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o: ../src/HungarianMatcher.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/HungarianMatcher.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/HungarianMatcher.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/HungarianMatcher.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.s

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o: ../src/KalmFilter3D.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/KalmFilter3D.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/KalmFilter3D.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/KalmFilter3D.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.s

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o: ../src/PointTracker.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/PointTracker.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/PointTracker.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/PointTracker.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.s

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o: ../src/Utils.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/Utils.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/Utils.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/Utils.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.s

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/flags.make
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o: CMakeFiles/MSHNet_TensorRT_Infer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o -MF CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o.d -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o -c /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/main.cpp

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/main.cpp > CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.i

CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/My_Project/MSHNet_TensorRT_Infer/src/main.cpp -o CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.s

# Object files for target MSHNet_TensorRT_Infer
MSHNet_TensorRT_Infer_OBJECTS = \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o" \
"CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o"

# External object files for target MSHNet_TensorRT_Infer
MSHNet_TensorRT_Infer_EXTERNAL_OBJECTS =

MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/DataGenerator.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make
MSHNet_TensorRT_Infer: CMakeFiles/MSHNet_TensorRT_Infer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable MSHNet_TensorRT_Infer"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/MSHNet_TensorRT_Infer.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/MSHNet_TensorRT_Infer.dir/build: MSHNet_TensorRT_Infer
.PHONY : CMakeFiles/MSHNet_TensorRT_Infer.dir/build

CMakeFiles/MSHNet_TensorRT_Infer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MSHNet_TensorRT_Infer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MSHNet_TensorRT_Infer.dir/clean

CMakeFiles/MSHNet_TensorRT_Infer.dir/depend:
	cd /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/My_Project/MSHNet_TensorRT_Infer /home/<USER>/My_Project/MSHNet_TensorRT_Infer /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles/MSHNet_TensorRT_Infer.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MSHNet_TensorRT_Infer.dir/depend

