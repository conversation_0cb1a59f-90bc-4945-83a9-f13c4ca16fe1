CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -I. -Iinclude
LDFLAGS =

SRCDIR = src
SOURCES = $(SRCDIR)/KalmFilter3D.cpp $(SRCDIR)/PointTracker.cpp $(SRCDIR)/HungarianMatcher.cpp $(SRCDIR)/Utils.cpp
OBJECTS = $(SOURCES:.cpp=.o)

test_tracker: test_tracker.cpp $(OBJECTS)
	$(CXX) $(CXXFLAGS) -o $@ $^ $(LDFLAGS)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) test_tracker

.PHONY: clean
