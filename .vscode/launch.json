{"version": "0.2.0", "configurations": [{"name": "Launch MSHNet_TensorRT_Infer", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/MSHNet_TensorRT_Infer", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "启用 pretty-printing", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake Build"}]}