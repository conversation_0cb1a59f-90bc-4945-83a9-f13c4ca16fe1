cmake_minimum_required(VERSION 3.10)
project(MSHNet_TensorRT_Infer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON) # 为 clangd 等工具生成 compile_commands.json

# 启用调试信息
set(CMAKE_BUILD_TYPE Debug)

# 添加头文件路径
include_directories(include)

# 查找 Eigen3
find_package(Eigen3 REQUIRED)
include_directories(${EIGEN3_INCLUDE_DIR})

# 源文件
file(GLOB_RECURSE SOURCES src/*.cpp)

# 可执行文件
add_executable(MSHNet_TensorRT_Infer ${SOURCES})
