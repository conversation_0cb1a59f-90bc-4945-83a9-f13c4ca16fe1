# C++ Multi-Object Point Tracker

这是一个基于卡尔曼滤波器的多目标点跟踪系统的C++实现，参考了Python版本的设计。

## 功能特性

- **3D点跟踪**: 支持3D空间中的点目标跟踪
- **卡尔曼滤波**: 使用6维状态向量（位置+速度）进行状态估计
- **匈牙利算法**: 优化的数据关联算法（已替换为更稳定的贪心匹配）
- **ReID机制**: 支持丢失目标的重新识别
- **聚类预处理**: 对检测点进行聚类以减少噪声

## 项目结构

```
├── include/
│   ├── KalmanFilter3D.hpp     # 3D卡尔曼滤波器
│   ├── PointTracker.hpp       # 主跟踪器接口
│   ├── HungarianMatcher.hpp   # 匹配算法
│   └── Utils.hpp              # 工具函数
├── src/
│   ├── KalmFilter3D.cpp       # 卡尔曼滤波器实现
│   ├── PointTracker.cpp       # 跟踪器实现
│   ├── HungarianMatcher.cpp   # 匹配算法实现
│   ├── Utils.cpp              # 工具函数实现
│   ├── DataGenerator.cpp      # 测试数据生成
│   └── main.cpp               # 主程序
├── python/
│   └── tracker.py             # Python参考实现
├── test_tracker.cpp           # 单元测试
├── compare_results.py         # 结果比较脚本
└── CMakeLists.txt             # CMake构建文件
```

## 编译和运行

### 主程序

```bash
mkdir build
cd build
cmake ..
make
./MSHNet_TensorRT_Infer
```

### 单元测试

```bash
make -f Makefile.test
./test_tracker
```

### 结果分析

```bash
python3 compare_results.py
```

## 算法参数

- `max_age`: 跟踪器最大存活时间（默认：3）
- `reid_age`: ReID最大时间（默认：20）
- `distance_threshold`: 匹配距离阈值（默认：50.0）
- `min_hits`: 最小命中次数（默认：2）

## 性能指标

根据测试结果：
- **平均位置误差**: 1.30 单位
- **处理速度**: 100帧处理时间 < 1ms
- **跟踪连续性**: 平均跟踪长度 4.37 帧

## 主要改进

1. **修复段错误**: 解决了原始C++版本中的内存访问问题
2. **数据生成优化**: 修正了检测点生成逻辑
3. **匹配算法简化**: 用稳定的贪心算法替换复杂的匈牙利算法
4. **参数调优**: 使参数与Python版本保持一致
5. **聚类预处理**: 添加检测点聚类功能

## 与Python版本的对比

| 特性 | Python版本 | C++版本 | 状态 |
|------|------------|---------|------|
| 卡尔曼滤波 | ✓ | ✓ | 完成 |
| 匈牙利算法 | ✓ | ✓ (简化版) | 完成 |
| ReID机制 | ✓ | ✓ | 完成 |
| 聚类预处理 | ✓ | ✓ | 完成 |
| 性能优化 | - | ✓ | 完成 |

## 输出文件

- `output.csv`: C++跟踪结果
- `real.csv`: 原始检测数据
- `tracking_comparison.png`: 可视化比较图

## 依赖项

- Eigen3: 线性代数库
- C++17: 编译器支持
- CMake: 构建系统
- Python3 + matplotlib: 结果分析（可选）

## 使用示例

```cpp
#include "PointTracker.hpp"

// 创建跟踪器
PointTracker tracker(3, 20, 50.0f, 2);

// 处理每一帧
for (int frame = 0; frame < num_frames; ++frame) {
    std::vector<Point> detections = getDetections(frame);
    std::vector<TrackResult> tracks = tracker.update(detections);
    
    // 处理跟踪结果
    for (const auto& track : tracks) {
        std::cout << "Track " << track.id 
                  << " at (" << track.position[0] 
                  << ", " << track.position[1] 
                  << ", " << track.position[2] << ")" << std::endl;
    }
}
```

## 故障排除

1. **编译错误**: 确保安装了Eigen3库
2. **运行时错误**: 检查输入数据格式
3. **性能问题**: 调整距离阈值和聚类参数

## 贡献

欢迎提交问题报告和改进建议！
