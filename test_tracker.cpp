#include <iostream>
#include <vector>
#include <cassert>
#include <cmath>
#include <chrono>
#include "include/PointTracker.hpp"
#include "include/KalmanFilter3D.hpp"
#include "include/Utils.hpp"

// 简单的测试框架
class TestFramework {
public:
    static void assert_equal(float a, float b, float tolerance = 1e-6) {
        if (std::abs(a - b) > tolerance) {
            std::cerr << "Assertion failed: " << a << " != " << b << std::endl;
            exit(1);
        }
    }
    
    static void assert_true(bool condition, const std::string& message = "") {
        if (!condition) {
            std::cerr << "Assertion failed: " << message << std::endl;
            exit(1);
        }
    }
    
    static void test_passed(const std::string& test_name) {
        std::cout << "[PASS] " << test_name << std::endl;
    }
};

// 测试卡尔曼滤波器
void test_kalman_filter() {
    std::cout << "Testing KalmanFilter3D..." << std::endl;
    
    Eigen::Vector3f init_pos(0.0f, 0.0f, 0.0f);
    KalmanFilter3D kf(init_pos);
    
    // 测试初始状态
    Eigen::Vector3f pred = kf.getPrediction();
    TestFramework::assert_equal(pred[0], 0.0f);
    TestFramework::assert_equal(pred[1], 0.0f);
    TestFramework::assert_equal(pred[2], 0.0f);
    
    // 测试预测
    kf.predict();
    TestFramework::assert_true(kf.getAge() == 1);
    TestFramework::assert_true(kf.getTimeSinceUpdate() == 1);
    
    // 测试更新
    Eigen::Vector3f measurement(1.0f, 1.0f, 1.0f);
    kf.update(measurement);
    TestFramework::assert_true(kf.getTimeSinceUpdate() == 0);
    TestFramework::assert_true(kf.getHitStreak() == 2);
    
    // 测试ID分配
    TestFramework::assert_true(!kf.hasId());
    kf.assignId();
    TestFramework::assert_true(kf.hasId());
    TestFramework::assert_true(kf.getId() >= 0);
    
    TestFramework::test_passed("KalmanFilter3D basic functionality");
}

// 测试点跟踪器
void test_point_tracker() {
    std::cout << "Testing PointTracker..." << std::endl;
    
    PointTracker tracker(3, 20, 50.0f, 2);
    
    // 创建测试数据
    std::vector<Point> detections1;
    Point p1;
    p1.position = {0.0f, 0.0f, 0.0f};
    p1.frame = 0;
    p1.label = -1;
    detections1.push_back(p1);
    
    Point p2;
    p2.position = {10.0f, 10.0f, 10.0f};
    p2.frame = 0;
    p2.label = -1;
    detections1.push_back(p2);
    
    // 第一帧
    std::vector<TrackResult> tracks1 = tracker.update(detections1);
    TestFramework::assert_true(tracks1.size() == 0); // min_hits=2，第一帧不应该有输出
    
    // 第二帧，相似位置
    std::vector<Point> detections2;
    Point p3;
    p3.position = {1.0f, 1.0f, 1.0f};
    p3.frame = 1;
    p3.label = -1;
    detections2.push_back(p3);
    
    Point p4;
    p4.position = {11.0f, 11.0f, 11.0f};
    p4.frame = 1;
    p4.label = -1;
    detections2.push_back(p4);
    
    std::vector<TrackResult> tracks2 = tracker.update(detections2);
    TestFramework::assert_true(tracks2.size() == 2); // 现在应该有2个跟踪目标
    
    TestFramework::test_passed("PointTracker basic functionality");
}

// 测试聚类功能
void test_clustering() {
    std::cout << "Testing clustering..." << std::endl;
    
    std::vector<Point> points;
    
    // 创建两个聚类
    Point p1; p1.position = {0.0f, 0.0f, 0.0f}; p1.frame = 0; p1.label = -1;
    Point p2; p2.position = {1.0f, 1.0f, 1.0f}; p2.frame = 0; p2.label = -1;
    Point p3; p3.position = {100.0f, 100.0f, 100.0f}; p3.frame = 0; p3.label = -1;
    Point p4; p4.position = {101.0f, 101.0f, 101.0f}; p4.frame = 0; p4.label = -1;
    
    points.push_back(p1);
    points.push_back(p2);
    points.push_back(p3);
    points.push_back(p4);
    
    std::vector<Point> clustered = clusterDetections(points, 5.0f);
    TestFramework::assert_true(clustered.size() == 2); // 应该聚类成2个点
    
    TestFramework::test_passed("Clustering functionality");
}

// 性能测试
void test_performance() {
    std::cout << "Testing performance..." << std::endl;
    
    PointTracker tracker(3, 20, 50.0f, 2);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 模拟100帧，每帧10个检测
    for (int frame = 0; frame < 100; ++frame) {
        std::vector<Point> detections;
        for (int i = 0; i < 10; ++i) {
            Point p;
            p.position = {float(i * 10), float(frame), 0.0f};
            p.frame = frame;
            p.label = -1;
            detections.push_back(p);
        }
        
        std::vector<TrackResult> tracks = tracker.update(detections);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "Performance test completed in " << duration.count() << " ms" << std::endl;
    TestFramework::assert_true(duration.count() < 1000); // 应该在1秒内完成
    
    TestFramework::test_passed("Performance test");
}

int main() {
    std::cout << "Running C++ Tracker Tests..." << std::endl;
    
    try {
        test_kalman_filter();
        test_point_tracker();
        test_clustering();
        test_performance();
        
        std::cout << "\nAll tests passed! ✓" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
