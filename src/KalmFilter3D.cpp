#include "KalmanFilter3D.hpp"
#include <cmath>

int KalmanFilter3D::next_id = 0;

KalmanFilter3D::KalmanFilter3D(const Eigen::Vector3f& init_pos)
    : age(0), time_since_update(0), hit_streak(1), id(-1), id_assigned(false)
{
    float dt = 1.0f;

    // 状态转移矩阵 （单位时间步）
    F.setIdentity();
    for (int i = 0; i < 3; ++i)
        F(i, i + 3) = dt;

    // 观测矩阵 （位置）
    H.setZero();
    H.block<3, 3>(0, 0) = Eigen::Matrix3f::Identity();

    //过程噪声和测量噪声
    Q = Eigen::Matrix<float, 6, 6>::Identity() * 0.1f;
    R = Eigen::Matrix3f::Identity() * 10.0f;

    // 协方差矩阵初始化
    P = Eigen::Matrix<float, 6, 6>::Identity() * 10.0f;

    // 状态向量初始化
    x.setZero();
    x.block<3, 1>(0, 0) = init_pos;

    history.clear();
    history.push_back(init_pos);
}

void KalmanFilter3D::predict() {
    x = F * x;
    P = F * P * F.transpose() + Q;

    history.push_back(x.block<3, 1>(0, 0));
    age++;
    time_since_update++;
}

void KalmanFilter3D::update(const Eigen::Vector3f& z) {
    Eigen::Vector3f y = z - H * x;
    Eigen::Matrix3f S = H * P * H.transpose() + R;
    Eigen::Matrix<float, 6, 3> K = P * H.transpose() * S.inverse();
    x = x + K * y;
    P = (Eigen::Matrix<float, 6, 6>::Identity() - K * H) * P;

    history.push_back(x.block<3, 1>(0, 0));
    
    time_since_update = 0;
    hit_streak++;

    // age = 0;
}

Eigen::Vector3f KalmanFilter3D::getPrediction() const {
    return x.block<3, 1>(0, 0);
}

int KalmanFilter3D::getId() const {
    return id;
}

int KalmanFilter3D::getAge() const {
    return age;
}

int KalmanFilter3D::getTimeSinceUpdate() const {
    return time_since_update;
}

int KalmanFilter3D::getHitStreak() const {
    return hit_streak;
}


std::vector<Eigen::Vector3f> KalmanFilter3D::getHistory() const {
    return history;
}

void KalmanFilter3D::assignId(){
    if (!id_assigned){
        id = next_id++;
        id_assigned = true;
    }
}

bool KalmanFilter3D::hasId() const {
    return id_assigned;
}

void KalmanFilter3D::reset(const Eigen::Vector3f& new_pos) {
    x.setZero();
    x.block<3, 1>(0, 0) = new_pos;

    P = Eigen::Matrix<float, 6, 6>::Identity() * 10.0f;

    history.clear();
    history.push_back(new_pos);

    age = 1;
    time_since_update = 0;
    hit_streak = 2;

    // id = -1;
    // id_assigned = false;
}
