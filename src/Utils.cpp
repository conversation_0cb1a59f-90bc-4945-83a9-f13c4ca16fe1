#include "Utils.hpp"
#include <fstream>
#include <iostream>
#include <cmath>
#include <algorithm>

void writeResultsToCSV(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return;
    }

    file << "frame,id,x,y,z\n";
    for (size_t frame = 0; frame < tracks.size(); ++frame) {
        for (const auto& obj : tracks[frame]) {
            file << frame << "," << obj.id << ","
                 << obj.position.x() << "," << obj.position.y() << "," << obj.position.z() << "\n";
        }
    }

    file.close();
    std::cout << "CSV写入完成: " << filename << std::endl;
}

void writeResultsToPLY(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开PLY文件: " << filename << std::endl;
        return;
    }

    // 统计总点数
    size_t total_points = 0;
    for (const auto& frame_data : tracks)
        total_points += frame_data.size();

    // 写入PLY头部
    file << "ply\nformat ascii 1.0\n";
    file << "element vertex " << total_points << "\n";
    file << "property float x\nproperty float y\nproperty float z\n";
    file << "property uchar red\nproperty uchar green\nproperty uchar blue\n";
    file << "end_header\n";

    // 写入每个点
    for (const auto& frame_data : tracks) {
        for (const auto& obj : frame_data) {
            // 通过 id 映射颜色（可改进）
            uint8_t r = (obj.id * 53) % 256;
            uint8_t g = (obj.id * 97) % 256;
            uint8_t b = (obj.id * 151) % 256;

            file << obj.position.x() << " " << obj.position.y() << " " << obj.position.z() << " ";
            file << static_cast<int>(r) << " " << static_cast<int>(g) << " " << static_cast<int>(b) << "\n";
        }
    }

    file.close();
    std::cout << "PLY写入完成: " << filename << std::endl;
}


void writePointsToCSV(const std::vector<std::vector<Point>>& points, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return;
    }

    file << "frame,id,x,y,z\n";
    for (size_t frame = 0; frame < points.size(); ++frame) {
        for (const auto& p : points[frame]) {
            if (p.label == -1){
                continue;
            }
            file << p.frame << "," << p.label << ","
                 << p.position[0] << "," << p.position[1] << "," << p.position[2] << "\n";
        }
    }

    file.close();
    std::cout << "原始点迹写入完成: " << filename << std::endl;
}

// 简单的聚类实现，基于距离的贪心聚类
std::vector<Point> clusterDetections(const std::vector<Point>& points, float eps) {
    if (points.empty()) {
        return std::vector<Point>();
    }

    std::vector<Point> clustered_points;
    std::vector<bool> used(points.size(), false);

    for (size_t i = 0; i < points.size(); ++i) {
        if (used[i]) continue;

        // 找到所有在eps距离内的点
        std::vector<size_t> cluster_indices;
        cluster_indices.push_back(i);
        used[i] = true;

        for (size_t j = i + 1; j < points.size(); ++j) {
            if (used[j]) continue;

            // 计算距离
            float dx = points[i].position[0] - points[j].position[0];
            float dy = points[i].position[1] - points[j].position[1];
            float dz = points[i].position[2] - points[j].position[2];
            float dist = std::sqrt(dx*dx + dy*dy + dz*dz);

            if (dist < eps) {
                cluster_indices.push_back(j);
                used[j] = true;
            }
        }

        // 计算聚类中心
        float center_x = 0, center_y = 0, center_z = 0;
        for (size_t idx : cluster_indices) {
            center_x += points[idx].position[0];
            center_y += points[idx].position[1];
            center_z += points[idx].position[2];
        }
        center_x /= cluster_indices.size();
        center_y /= cluster_indices.size();
        center_z /= cluster_indices.size();

        // 创建聚类中心点
        Point cluster_center;
        cluster_center.position[0] = center_x;
        cluster_center.position[1] = center_y;
        cluster_center.position[2] = center_z;
        cluster_center.frame = points[i].frame;
        cluster_center.label = points[i].label;

        clustered_points.push_back(cluster_center);
    }

    return clustered_points;
}
