#include <iostream>
#include <vector>
#include <fstream>
#include "DataGenerator.hpp"
#include "PointTracker.hpp"
#include "Utils.hpp"

int main() {
    const int num_frames = 100;
    const int num_targets = 5;
    const int drop_interval = 5;
    const float noise_std = 3.0f;

    // ---------- 1. 生成模拟点迹 ----------
    DataGenerator generator;
    std::vector<std::vector<Point>> all_detections =
        generator.generate(num_frames, num_targets, drop_interval, noise_std);

    std::cout << "生成完毕，每帧目标点数（含杂波）：" 
              << all_detections[0].size() << std::endl;

    // ---------- 2. 初始化跟踪器 ----------
    // 使用与Python版本相同的参数：max_age=3, distance_threshold=50, reid_age=20, min_hits=2
    PointTracker tracker(3, 20, 50.0f, 2);
    std::vector<std::vector<TrackResult>> tracked_results;

    // ---------- 3. 遍历每一帧 ----------
    for (int frame = 0; frame < num_frames; ++frame) {
        const auto& detections_raw = all_detections[frame];

        std::vector<Point> detections;
        for (const auto& vec : detections_raw) {
            Point p;
            p.position = vec.position;
            p.frame = frame;
            p.label = -1;  // 若你没有标签信息，则设为杂波
            detections.push_back(p);
        }

        // 对检测进行聚类
        std::vector<Point> clustered = clusterDetections(detections, 30.0f);

        std::vector<TrackResult> tracks = tracker.update(clustered);
        tracked_results.push_back(tracks);

        // 显示进度
        if (frame % 10 == 0) {
            std::cout << "Processing frame " << frame << "/" << num_frames
                      << ", tracks: " << tracks.size() << std::endl;
        }
    }

    // ---------- 4. 输出结果 ----------
    writeResultsToCSV(tracked_results, "output.csv");
    writePointsToCSV(all_detections,"real.csv");

    std::cout << "跟踪完成，结果已保存。" << std::endl;
    return 0;
}
