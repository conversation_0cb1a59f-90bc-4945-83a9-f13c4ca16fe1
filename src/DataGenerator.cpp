// DataGenerator.cpp
#include "DataGenerator.hpp"
#include <random>
#include <array>
#include <cmath>

std::vector<std::vector<Point>> DataGenerator::generate(
    int num_frames,
    int num_targets,
    int drop_interval,
    float noise_std
) {
    std::vector<std::vector<Point>> all_frames;
    // std::random_device rd;
    // std::mt19937 rng(rd());
    std::mt19937 rng(36);
    std::uniform_real_distribution<float> pos_dist(-500.0f, 500.0f);
    std::uniform_real_distribution<float> vel_dist(-5.0f, 5.0f);
    std::uniform_real_distribution<float> clutter_dist(-600.0f, 600.0f);
    std::normal_distribution<float> noise(0.0f, noise_std);
    std::uniform_real_distribution<float> drop_chance(0.0f, 1.0f);
    std::uniform_int_distribution<int> clutter_num_dist(1, 4);

    // 初始位置与速度
    std::vector<std::array<float, 3>> base_positions(num_targets);
    std::vector<std::array<float, 3>> base_velocities(num_targets);

    for (int i = 0; i < num_targets; ++i) {
        for (int j = 0; j < 3; ++j) {
            base_positions[i][j] = pos_dist(rng);
            base_velocities[i][j] = vel_dist(rng);
        }
    }

    for (int frame = 0; frame < num_frames; ++frame) {
        std::vector<Point> frame_points;

        for (int i = 0; i < num_targets; ++i) {
            std::array<float, 3> velocity = base_velocities[i];
            for (int j = 0; j < 3; ++j) {
                velocity[j] += 2.0f * std::sin(frame / 50.0f + i);
            }

            std::array<float, 3> pos;
            for (int j = 0; j < 3; ++j) {
                pos[j] = base_positions[i][j] + frame * velocity[j];
                pos[j] += noise(rng); // 添加噪声
            }

            if (drop_interval > 0 && frame % drop_interval == 0) {
                if (drop_chance(rng) < 0.5f) {
                    continue;
                }
            }

            frame_points.push_back(Point{pos, frame, i});
        }

        int num_clutter = clutter_num_dist(rng);
        for (int c = 0; c < num_clutter; ++c) {
            std::array<float, 3> pos = {clutter_dist(rng), clutter_dist(rng), clutter_dist(rng)};
            frame_points.push_back(Point{pos, frame, -1}); // 杂波点
        }

        all_frames.push_back(frame_points);
    }

    return all_frames;
}
