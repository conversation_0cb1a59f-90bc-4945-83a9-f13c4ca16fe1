import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# Read both data files
df_track = pd.read_csv('output.csv')
df_real = pd.read_csv('real.csv')

# Get unique IDs from both datasets
track_ids = df_track['id'].unique()
real_ids = df_real['id'].unique()
all_ids = set(track_ids).union(set(real_ids))

# Create a color map for all IDs
colors = plt.cm.get_cmap('tab20', len(all_ids))
id_to_color = {id: colors(i) for i, id in enumerate(all_ids)}

# Set up the figure
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')

# Set initial axis limits using both datasets
ax.set_xlim(min(df_track['x'].min(), df_real['x'].min()), 
            max(df_track['x'].max(), df_real['x'].max()))
ax.set_ylim(min(df_track['y'].min(), df_real['y'].min()), 
            max(df_track['y'].max(), df_real['y'].max()))
ax.set_zlim(min(df_track['z'].min(), df_real['z'].min()), 
            max(df_track['z'].max(), df_real['z'].max()))

ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.set_zlabel('Z')
ax.set_title('Tracked (solid) vs Real (dashed) Trajectories')

# Calculate the maximum frame number from both datasets
max_frame = max(df_track['frame'].max(), df_real['frame'].max())

legend_added = False

def update(frame):
    global legend_added
    ax.clear()
    
    # 设置坐标轴
    ax.set_xlim(min(df_track['x'].min(), df_real['x'].min()), 
                max(df_track['x'].max(), df_real['x'].max()))
    ax.set_ylim(min(df_track['y'].min(), df_real['y'].min()), 
                max(df_track['y'].max(), df_real['y'].max()))
    ax.set_zlim(min(df_track['z'].min(), df_real['z'].min()), 
                max(df_track['z'].max(), df_real['z'].max()))
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(f'Frame {frame}: Tracked (solid) vs Real (dashed)')
    
    # 绘制跟踪轨迹
    for id in track_ids:
        track = df_track[(df_track['id'] == id) & (df_track['frame'] <= frame)]
        if not track.empty:
            ax.plot(track['x'], track['y'], track['z'], 
                    color=id_to_color[id], 
                    linestyle='-',
                    linewidth=2,
                    label=f'Tracked {id}' if not legend_added else ""
                    )
    
    # 绘制真实轨迹
    for id in real_ids:
        real = df_real[(df_real['id'] == id) & (df_real['frame'] <= frame)]
        if not real.empty:
            ax.plot(real['x'], real['y'], real['z'], 
                    color='gray', 
                    linestyle='--', 
                    linewidth=2,
                    label=f'Real {id}' if not legend_added else ""
                    )
    
    # 只在第一帧添加图例
    if not legend_added:
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        legend_added = True

# 创建动画
ani = FuncAnimation(fig, update, frames=int(max_frame)+1, interval=200)

plt.tight_layout()
plt.show()