#!/usr/bin/env python3
"""
简化的雷达数据生成测试
验证360度扫描雷达模拟数据生成功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd

def generate_radar_scan_data(num_frames=120, num_targets=5, scan_period=6.0, 
                           beam_width=3.0, fps=20, noise_std=3.0, detection_range=1000):
    """
    生成360度扫描雷达模拟数据（优化版本）
    
    参数说明:
    - 6秒旋转一圈 (scan_period=6)
    - 每帧3度数据 (beam_width=3) 
    - 一秒20帧数据 (fps=20)
    - 总共120帧 = 6秒 * 20fps
    """

    print("=== 雷达扫描参数 ===")
    print(f"扫描周期: {scan_period}秒 (360度)")
    print(f"帧率: {fps} fps")
    print(f"波束宽度: {beam_width}度")
    print(f"每帧时间间隔: {1.0/fps:.3f}秒")
    print(f"每帧扫描角度: {360.0/(scan_period*fps):.1f}度")
    print(f"总帧数: {num_frames}")
    print(f"目标数量: {num_targets}")
    print(f"探测逻辑: 波束内目标检测概率恒定90%，无距离限制")
    print()

    np.random.seed(42)

    # 初始化目标
    targets = []
    for i in range(num_targets):
        angle = np.random.uniform(0, 2*np.pi)
        radius = np.random.uniform(200, 800)
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        z = np.random.uniform(-50, 50)
        vx = np.random.uniform(-20, 20)
        vy = np.random.uniform(-20, 20)
        vz = np.random.uniform(-5, 5)

        targets.append({
            'id': i,
            'pos': np.array([x, y, z]),
            'vel': np.array([vx, vy, vz])
        })

    # 每帧处理
    all_frames = []
    frame_time_interval = 1.0 / fps
    degrees_per_frame = 360.0 / (scan_period * fps)

    for frame in range(num_frames):
        current_time = frame * frame_time_interval
        beam_angle = (frame * degrees_per_frame) % 360.0
        frame_data = []

        for target in targets:
            # 更新目标位置
            pos = target['pos'] + target['vel'] * current_time
            target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
            target_range = np.linalg.norm(pos[:2])

            # 判断是否在波束范围
            beam_start = beam_angle
            beam_end = (beam_angle + beam_width) % 360

            if beam_end < beam_start:
                in_beam = (target_azimuth >= beam_start) or (target_azimuth <= beam_end)
            else:
                in_beam = beam_start <= target_azimuth <= beam_end

            if in_beam:
                detection_prob = 1  # 恒定概率

                if np.random.rand() < detection_prob:
                    noise = np.random.normal(0, noise_std, size=3)
                    noisy_pos = pos + noise
                    frame_data.append({
                        'x': noisy_pos[0],
                        'y': noisy_pos[1], 
                        'z': noisy_pos[2],
                        'time': current_time,
                        'target_id': target['id'],
                        'beam_angle': beam_angle,
                        'true_azimuth': target_azimuth,
                        'range': target_range
                    })

        # 添加杂波（可选保留探测范围）
        clutter_density = 0.05
        expected_clutter = min(0.1, beam_width * clutter_density)
        num_clutter = np.random.poisson(expected_clutter)

        for _ in range(num_clutter):
            clutter_azimuth = np.random.uniform(beam_angle, beam_angle + beam_width) % 360
            clutter_range = np.random.uniform(100, detection_range)
            clutter_elevation = np.random.uniform(-100, 100)
            clutter_x = clutter_range * np.cos(np.radians(clutter_azimuth))
            clutter_y = clutter_range * np.sin(np.radians(clutter_azimuth))
            clutter_z = clutter_elevation

            frame_data.append({
                'x': clutter_x,
                'y': clutter_y,
                'z': clutter_z,
                'time': current_time,
                'target_id': -1,
                'beam_angle': beam_angle,
                'true_azimuth': clutter_azimuth,
                'range': clutter_range
            })

        all_frames.append(frame_data)

        if frame % 20 == 0:
            detections = len([d for d in frame_data if d['target_id'] != -1])
            clutter = len([d for d in frame_data if d['target_id'] == -1])
            print(f"帧 {frame:3d}: 波束角度 {beam_angle:6.1f}°, "
                  f"目标检测 {detections}, 杂波 {clutter}")

    return all_frames, targets

def analyze_radar_data(frames, targets):
    """分析雷达数据统计"""
    print("\n=== 数据统计分析 ===")
    
    total_detections = sum(len(frame) for frame in frames)
    target_detections = sum(len([d for d in frame if d['target_id'] != -1]) for frame in frames)
    clutter_detections = total_detections - target_detections
    
    print(f"总检测点数: {total_detections}")
    print(f"目标检测点数: {target_detections}")
    print(f"杂波点数: {clutter_detections}")
    print(f"杂波比例: {clutter_detections/total_detections*100:.1f}%")
    
    # 每个目标的检测统计
    print(f"\n各目标检测统计:")
    for target in targets:
        target_count = sum(len([d for d in frame if d['target_id'] == target['id']]) for frame in frames)
        print(f"  目标 {target['id']}: {target_count} 次检测")
    
    # 每帧检测数量统计
    frame_counts = [len(frame) for frame in frames]
    print(f"\n每帧检测统计:")
    print(f"  平均每帧: {np.mean(frame_counts):.1f}")
    print(f"  最少: {np.min(frame_counts)}")
    print(f"  最多: {np.max(frame_counts)}")

def save_radar_data(frames, filename="radar_data.csv"):
    """保存雷达数据到CSV"""
    all_data = []
    for frame_idx, frame in enumerate(frames):
        for detection in frame:
            detection['frame'] = frame_idx
            all_data.append(detection)
    
    df = pd.DataFrame(all_data)
    df.to_csv(filename, index=False)
    print(f"\n数据已保存到: {filename}")
    return df

def visualize_radar_beam_animation(frames, targets, save_plot=True):
    """逐帧展示雷达扫描波束和目标检测"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 准备颜色映射
    target_colors = plt.cm.tab10(np.linspace(0, 1, len(targets)))
    color_map = {target['id']: target_colors[i] for i, target in enumerate(targets)}

    # 计算所有目标的完整轨迹
    target_trajectories = {}
    for target in targets:
        trajectory = []
        for frame_idx in range(len(frames)):
            current_time = frame_idx * 0.05
            # 计算目标在该时刻的真实位置
            pos = target['pos'] + target['vel'] * current_time
            trajectory.append(pos)
        target_trajectories[target['id']] = np.array(trajectory)

    for frame_idx, frame in enumerate(frames):
        # 清空图形
        ax1.clear()
        ax2.clear()

        # 当前波束角度
        beam_angle = (frame_idx * 3) % 360
        beam_start = beam_angle
        beam_end = (beam_angle + 3) % 360

        # 子图1: 极坐标雷达扫描视图
        ax1 = plt.subplot(121, projection='polar')
        ax1.set_title(f'Radar Beam Scan - Frame {frame_idx}\nBeam: {beam_angle:.1f}°-{beam_end:.1f}°')
        ax1.set_ylim(0, 1000)

        # 绘制雷达波束扇区
        beam_angles = np.linspace(np.radians(beam_start), np.radians(beam_end), 50)
        if beam_end < beam_start:  # 跨越0度
            beam_angles1 = np.linspace(np.radians(beam_start), np.radians(360), 25)
            beam_angles2 = np.linspace(np.radians(0), np.radians(beam_end), 25)
            beam_angles = np.concatenate([beam_angles1, beam_angles2])

        beam_ranges = np.full_like(beam_angles, 1000)
        ax1.fill_between(beam_angles, 0, beam_ranges, alpha=0.2, color='yellow', label='Current Beam')

        # 绘制当前帧检测到的目标（红色三角形）
        for detection in frame:
            r = detection['range']
            theta = np.radians(detection['true_azimuth'])
            if detection['target_id'] != -1:  # 真实目标
                ax1.scatter(theta, r, c='red', marker='^', s=100, alpha=0.8,
                           label='Detected Target' if frame_idx == 0 else "")
            else:  # 杂波
                ax1.scatter(theta, r, c='gray', marker='o', s=20, alpha=0.5,
                           label='Clutter' if frame_idx == 0 else "")

        # 绘制所有目标的当前位置（不管是否被检测到）
        for target in targets:
            current_time = frame_idx * 0.05
            pos = target['pos'] + target['vel'] * current_time
            target_range = np.linalg.norm(pos[:2])
            target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360

            if target_range <= 1000:  # 在雷达范围内
                theta = np.radians(target_azimuth)
                ax1.scatter(theta, target_range, c=color_map[target['id']],
                           marker='o', s=60, alpha=0.7, edgecolors='black', linewidth=1)

        if frame_idx == 0:
            ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

        # 子图2: 笛卡尔坐标目标轨迹视图
        ax2.set_title(f'Target Trajectories - Frame {frame_idx}')
        ax2.set_xlim(-1000, 1000)
        ax2.set_ylim(-1000, 1000)
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.grid(True, alpha=0.3)
        ax2.set_aspect('equal')

        # 绘制雷达波束扇区（笛卡尔坐标）
        beam_x = [0]
        beam_y = [0]
        for angle in np.linspace(beam_start, beam_end, 50):
            if beam_end < beam_start and angle > 180:  # 处理跨越0度的情况
                continue
            x = 1000 * np.cos(np.radians(angle))
            y = 1000 * np.sin(np.radians(angle))
            beam_x.append(x)
            beam_y.append(y)
        beam_x.append(0)
        beam_y.append(0)
        ax2.fill(beam_x, beam_y, alpha=0.2, color='yellow', label='Current Beam')

        # 绘制所有目标的完整轨迹（不同颜色）
        for target in targets:
            trajectory = target_trajectories[target['id']]
            ax2.plot(trajectory[:frame_idx+1, 0], trajectory[:frame_idx+1, 1],
                    color=color_map[target['id']], linewidth=2, alpha=0.7,
                    label=f'Target {target["id"]} Path')

            # 当前位置
            current_pos = trajectory[frame_idx]
            ax2.scatter(current_pos[0], current_pos[1],
                       color=color_map[target['id']], s=100, alpha=0.8,
                       edgecolors='black', linewidth=2)

        # 绘制当前帧检测到的目标（红色三角形）
        for detection in frame:
            if detection['target_id'] != -1:  # 真实目标
                ax2.scatter(detection['x'], detection['y'], c='red', marker='^',
                           s=150, alpha=0.9, edgecolors='darkred', linewidth=2,
                           label='Detected in Beam' if frame_idx == 0 else "")
            else:  # 杂波
                ax2.scatter(detection['x'], detection['y'], c='gray', marker='o',
                           s=30, alpha=0.5)

        if frame_idx == 0:
            ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        plt.tight_layout()
        plt.pause(0.05)  # 动画延迟

        # 保存关键帧
        if save_plot and frame_idx % 30 == 0:
            plt.savefig(f'radar_frame_{frame_idx:03d}.png', dpi=150, bbox_inches='tight')

    if save_plot:
        plt.savefig('radar_final_frame.png', dpi=150, bbox_inches='tight')
        print("雷达扫描动画帧已保存")

    plt.show()

def visualize_radar_coverage(frames, targets, save_plot=True):
    """可视化雷达覆盖和检测（静态总览）"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 子图1: 极坐标显示雷达覆盖
    ax1 = plt.subplot(121, projection='polar')

    # 绘制所有检测点
    for frame_idx, frame in enumerate(frames[::10]):  # 每10帧取一帧
        for detection in frame:
            if detection['target_id'] != -1:  # 目标
                r = detection['range']
                theta = np.radians(detection['true_azimuth'])
                ax1.scatter(theta, r, c='red', s=10, alpha=0.6)
            else:  # 杂波
                r = detection['range']
                theta = np.radians(detection['true_azimuth'])
                ax1.scatter(theta, r, c='gray', s=5, alpha=0.3)

    ax1.set_title('radar coverage (polar coordinates)')
    ax1.set_ylim(0, 1000)

    # 子图2: 笛卡尔坐标显示
    ax2 = plt.subplot(122)

    # 绘制目标轨迹
    for target in targets:
        trajectory_x = []
        trajectory_y = []
        for frame_idx, frame in enumerate(frames):
            for detection in frame:
                if detection['target_id'] == target['id']:
                    trajectory_x.append(detection['x'])
                    trajectory_y.append(detection['y'])

        if trajectory_x:
            ax2.plot(trajectory_x, trajectory_y, 'o-', label=f'target {target["id"]}', markersize=3)

    # 绘制杂波
    clutter_x = []
    clutter_y = []
    for frame in frames[::5]:  # 每5帧取一帧杂波
        for detection in frame:
            if detection['target_id'] == -1:
                clutter_x.append(detection['x'])
                clutter_y.append(detection['y'])

    ax2.scatter(clutter_x, clutter_y, c='gray', s=1, alpha=0.3, label='noise')
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('target trajectory and noise')
    ax2.legend()
    ax2.grid(True)
    ax2.axis('equal')

    plt.tight_layout()
    if save_plot:
        plt.savefig('radar_simulation.png', dpi=150, bbox_inches='tight')
        print("可视化图已保存到: radar_simulation.png")
    plt.show()

def main():
    """主测试函数"""
    print("360度扫描雷达模拟数据生成测试")
    print("=" * 50)
    
    # 生成雷达数据
    frames, targets = generate_radar_scan_data(
        num_frames=1200,    # 6秒 * 20fps = 120帧
        num_targets=5,     # 5个目标
        scan_period=6.0,   # 6秒一圈
        beam_width=3.0,    # 3度波束宽度
        fps=20,            # 20fps
        noise_std=3.0      # 3米噪声标准差
    )
    
    # 分析数据
    analyze_radar_data(frames, targets)
    
    # 保存数据
    save_radar_data(frames, "radar_simulation_data.csv")

    # 静态可视化
    print("\n生成静态覆盖图...")
    visualize_radar_coverage(frames, targets)

    # 动画可视化（逐帧展示雷达扫描）
    print("\n生成雷达扫描动画...")
    visualize_radar_beam_animation(frames, targets)

    print("\n测试完成！")

if __name__ == '__main__':
    main()
