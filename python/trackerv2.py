import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from filterpy.kalman import KalmanFilter
from scipy.optimize import linear_sum_assignment
from collections import defaultdict
import warnings
import pandas as pd
warnings.filterwarnings("ignore", category=np.VisibleDeprecationWarning)

# ---------- 数据读取与聚类 ----------
def load_radar_data_from_csv(csv_file='radar_simulation_data.csv'):
    """
    从CSV文件读取雷达点迹数据

    参数:
    - csv_file: CSV文件路径

    返回:
    - frames: 按帧组织的数据列表，每帧包含该帧的所有检测点
    - frame_info: 帧信息字典，包含时间、波束角度等
    """
    try:
        # 读取CSV数据
        df = pd.read_csv(csv_file)
        print(f"成功读取CSV文件: {csv_file}")
        print(f"数据总数: {len(df)} 个点")

        # 检查必要的列
        required_cols = ['x', 'y', 'z', 'time', 'target_id', 'frame']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"CSV文件缺少必要的列: {missing_cols}")

        # 统计信息
        true_targets = df[df['target_id'] != -1]
        clutter_points = df[df['target_id'] == -1]
        print(f"真实目标点数: {len(true_targets)}")
        print(f"杂波点数: {len(clutter_points)}")

        unique_targets = true_targets['target_id'].unique()
        print(f"目标数量: {len(unique_targets)}, 目标ID: {sorted(unique_targets)}")

        # 按帧组织数据
        frames = []
        frame_info = {}

        # 获取所有帧号并排序
        frame_numbers = sorted(df['frame'].unique())
        print(f"帧范围: {frame_numbers[0]} - {frame_numbers[-1]}")

        for frame_num in frame_numbers:
            frame_data = df[df['frame'] == frame_num]

            # 构建点数据：[x, y, z, time, target_id]
            points = []
            for _, row in frame_data.iterrows():
                point = np.array([row['x'], row['y'], row['z'], row['time'], row['target_id']])
                points.append(point)

            frames.append(np.array(points) if points else np.empty((0, 5)))

            # 保存帧信息
            if len(frame_data) > 0:
                frame_info[frame_num] = {
                    'time': frame_data['time'].iloc[0],
                    'beam_angle': frame_data['beam_angle'].iloc[0] if 'beam_angle' in frame_data.columns else None,
                    'num_detections': len(frame_data),
                    'num_targets': len(frame_data[frame_data['target_id'] != -1]),
                    'num_clutter': len(frame_data[frame_data['target_id'] == -1])
                }

        print(f"成功加载 {len(frames)} 帧数据")

        # 打印每个目标的统计信息
        print("\n=== 目标统计信息 ===")
        for tid in sorted(unique_targets):
            target_data = true_targets[true_targets['target_id'] == tid]
            time_range = target_data['time']
            print(f"目标 {tid}: {len(target_data)} 个检测点, "
                  f"时间范围: {time_range.min():.2f} - {time_range.max():.2f} 秒")

        return frames, frame_info

    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        print("将使用原始的模拟数据生成函数")
        return generate_synthetic_radar_data_backup()

def generate_synthetic_radar_data_backup(num_frames=120, num_targets=5, scan_period=6.0, beam_width=3.0,
                                 fps=20, noise_std=3.0, detection_range=1000):
    """
    生成360度扫描雷达模拟数据（备份函数）

    参数:
    - num_frames: 总帧数 (默认120帧 = 6秒 * 20fps)
    - num_targets: 目标数量
    - scan_period: 扫描周期(秒) - 6秒旋转一圈
    - beam_width: 波束宽度(度) - 每帧3度数据
    - fps: 帧率 - 一秒20帧
    - noise_std: 噪声标准差
    - detection_range: 雷达探测距离(米)
    """
    data = []
    np.random.seed(42)

    # 初始化目标状态（确保在雷达检测范围内）
    base_positions = np.random.uniform(-300, 300, size=(num_targets, 3))  # 减小初始范围
    base_velocities = np.random.uniform(-8, 8, size=(num_targets, 3))  # 减小速度避免快速离开

    # 计算时间参数
    frame_time_interval = 1.0 / fps  # 每帧时间间隔 = 0.05秒
    degrees_per_frame = 360.0 / (scan_period * fps)  # 每帧扫描角度 = 3度

    print(f"雷达参数:")
    print(f"  扫描周期: {scan_period}秒")
    print(f"  帧率: {fps}fps")
    print(f"  每帧时间间隔: {frame_time_interval:.3f}秒")
    print(f"  每帧扫描角度: {degrees_per_frame:.1f}度")
    print(f"  总帧数: {num_frames}")

    for frame in range(num_frames):
        current_time = frame * frame_time_interval
        current_beam_angle = (frame * degrees_per_frame) % 360.0  # 当前波束角度
        points = []

        for i in range(num_targets):
            # 目标运动模型 - 匀速直线运动 + 轻微机动
            maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
            pos = (base_positions[i] +
                   base_velocities[i] * current_time +
                   maneuver * current_time)

            # 计算目标相对雷达的方位角
            target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
            target_range = np.linalg.norm(pos[:2])  # 水平距离

            # 雷达波束覆盖检查
            beam_start = current_beam_angle
            beam_end = (current_beam_angle + beam_width) % 360

            # 处理跨越0度的情况
            if beam_end < beam_start:  # 跨越0度
                in_beam = (target_azimuth >= beam_start) or (target_azimuth <= beam_end)
            else:
                in_beam = beam_start <= target_azimuth <= beam_end

            # 距离检查
            in_range = target_range <= detection_range

            # 高检测概率模型（用于测试）
            if in_beam and in_range:
                # 非常高的基础检测概率
                base_prob = 0.99

                # 很小的距离衰减
                range_factor = max(0.9, 1.0 - target_range / detection_range * 0.1)

                # 很小的波束边缘衰减
                beam_center = (beam_start + beam_width/2) % 360
                angle_diff = min(abs(target_azimuth - beam_center),
                               360 - abs(target_azimuth - beam_center))
                beam_factor = max(0.95, 1.0 - angle_diff / (beam_width/2 + 1.0))  # 增加1度容差

                detection_prob = base_prob * range_factor * beam_factor

                if np.random.rand() < detection_prob:
                    # 添加测量噪声
                    noise = np.random.normal(0, noise_std, size=3)
                    # 距离噪声与距离成正比
                    range_noise_factor = 1 + target_range / 1000.0
                    noise[:2] *= range_noise_factor

                    noisy_pos = pos + noise
                    points.append(np.hstack((noisy_pos, current_time, i)))

        # 添加杂波 - 只在当前波束扇区内（适中密度）
        clutter_density = 0.3  # 每度杂波密度 - 适中
        expected_clutter = int(beam_width * clutter_density)
        num_clutter = np.random.poisson(expected_clutter)

        for _ in range(num_clutter):
            # 在当前波束扇区内随机生成杂波
            clutter_azimuth = np.random.uniform(current_beam_angle,
                                              current_beam_angle + beam_width) % 360
            clutter_range = np.random.uniform(50, detection_range)
            clutter_elevation = np.random.uniform(-50, 50)

            # 转换为笛卡尔坐标
            clutter_x = clutter_range * np.cos(np.radians(clutter_azimuth))
            clutter_y = clutter_range * np.sin(np.radians(clutter_azimuth))
            clutter_z = clutter_elevation

            clutter_pos = np.array([clutter_x, clutter_y, clutter_z])
            points.append(np.hstack((clutter_pos, current_time, -1)))  # -1表示杂波

        data.append(np.array(points) if points else np.empty((0, 5)))

        # 每30帧打印一次进度和调试信息
        if frame % 30 == 0:
            target_info = []
            for i in range(num_targets):
                current_time = frame * frame_time_interval
                maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
                pos = (base_positions[i] +
                       base_velocities[i] * current_time +
                       maneuver * current_time)
                target_range = np.linalg.norm(pos[:2])
                target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
                target_info.append(f"T{i}:r={target_range:.0f}m,az={target_azimuth:.0f}°")

            print(f"生成帧 {frame}/{num_frames}, 波束角度: {current_beam_angle:.1f}°, "
                  f"检测点数: {len(points)}")
            print(f"  目标状态: {' '.join(target_info)}")

    # 创建帧信息字典（与CSV读取函数保持一致）
    frame_info = {}
    for frame_idx in range(num_frames):
        current_time = frame_idx * frame_time_interval
        current_beam_angle = (frame_idx * degrees_per_frame) % 360.0
        frame_info[frame_idx] = {
            'time': current_time,
            'beam_angle': current_beam_angle,
            'num_detections': len(data[frame_idx]) if frame_idx < len(data) else 0,
            'num_targets': len([p for p in data[frame_idx] if len(p) > 4 and p[4] != -1]) if frame_idx < len(data) else 0,
            'num_clutter': len([p for p in data[frame_idx] if len(p) > 4 and p[4] == -1]) if frame_idx < len(data) else 0
        }

    return data, frame_info

def cluster_detections(points, eps=0.5, min_samples=1):
    """DBSCAN聚类检测点"""
    if len(points) == 0:
        return np.empty((0, 5))
    
    # 标准化坐标后聚类
    scaled = StandardScaler().fit_transform(points[:, :3])
    labels = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(scaled)
    
    # 合并聚类点
    clustered = []
    for lbl in np.unique(labels):
        if lbl == -1:  # 噪声点跳过
            continue
        cluster = points[labels == lbl]
        centroid = np.mean(cluster[:, :3], axis=0)
        timestamp = np.min(cluster[:, 3])  # 取最早时间戳
        clustered.append(np.hstack((centroid, timestamp)))
    
    return np.array(clustered) if clustered else np.empty((0, 4))

# ---------- 改进的跟踪器 ----------
class ImprovedKalmanTracker:
    id_counter = 0

    def __init__(self, point):
        """初始化跟踪器，固定使用匀加速模型"""
        self.last_time = point[3]

        # 固定使用匀加速模型（9维状态：位置、速度、加速度）
        self.kf = KalmanFilter(dim_x=9, dim_z=3)
        self._init_ca_filter()

        # 初始化状态向量：[x, y, z, vx, vy, vz, ax, ay, az]
        self.kf.x[:3] = point[:3].reshape(-1, 1)
        # 初始速度和加速度设为0
        self.kf.x[3:6] = 0
        self.kf.x[6:9] = 0

        self.id = None
        self.time_since_update = 0
        self.hit_streak = 1
        self.age = 0
        self.history = []
        self.prediction_history = []
    
    def _init_ca_filter(self):
        """初始化匀加速模型滤波器（优化参数）"""
        dt = 1.0  # 初始值，后续动态更新

        # 状态转移矩阵 F (9x9) - 匀加速模型
        # 状态向量: [x, y, z, vx, vy, vz, ax, ay, az]
        self.kf.F = np.array([
            [1,0,0, dt,0,0, 0.5*dt**2,0,0],
            [0,1,0, 0,dt,0, 0,0.5*dt**2,0],
            [0,0,1, 0,0,dt, 0,0,0.5*dt**2],
            [0,0,0, 1,0,0, dt,0,0],
            [0,0,0, 0,1,0, 0,dt,0],
            [0,0,0, 0,0,1, 0,0,dt],
            [0,0,0, 0,0,0, 1,0,0],
            [0,0,0, 0,0,0, 0,1,0],
            [0,0,0, 0,0,0, 0,0,1]
        ])

        # 观测矩阵 H (3x9) - 只观测位置
        self.kf.H = np.eye(3, 9)

        # 观测噪声协方差矩阵 R (3x3) - 根据雷达精度调整
        self.kf.R = np.diag([25, 25, 100])  # x,y精度较高，z精度较低

        # 初始状态协方差矩阵 P (9x9)
        self.kf.P = np.diag([100, 100, 100,    # 位置不确定性
                            50, 50, 50,        # 速度不确定性
                            25, 25, 25])       # 加速度不确定性

        # 过程噪声协方差矩阵 Q (9x9) - 针对稀疏数据（6秒间隔）优化
        self.kf.Q = np.diag([10.0, 10.0, 10.0,  # 增加位置过程噪声 - 适应长时间预测
                            50.0, 50.0, 50.0,   # 大幅增加速度过程噪声
                            100.0, 100.0, 100.0]) # 大幅增加加速度过程噪声
    

    
    def predict(self, current_time):
        """动态时间步长预测（固定匀加速模型）"""
        dt = max(0.05, current_time - self.last_time)  # 最小0.05秒防止数值问题

        # 更新状态转移矩阵的时间相关项
        self.kf.F[0,3] = dt
        self.kf.F[1,4] = dt
        self.kf.F[2,5] = dt
        self.kf.F[0,6] = 0.5*dt**2
        self.kf.F[1,7] = 0.5*dt**2
        self.kf.F[2,8] = 0.5*dt**2
        self.kf.F[3,6] = dt
        self.kf.F[4,7] = dt
        self.kf.F[5,8] = dt

        # 动态调整过程噪声（针对稀疏数据优化）
        dt_factor = min(dt / 1.0, 10.0)  # 基于1秒基准，最大因子为10
        self.kf.Q[:3,:3] = np.eye(3) * (10.0 * dt_factor)      # 大幅增加位置噪声
        self.kf.Q[3:6,3:6] = np.eye(3) * (50.0 * dt_factor)    # 大幅增加速度噪声
        self.kf.Q[6:,6:] = np.eye(3) * (100.0 * dt_factor**2)  # 大幅增加加速度噪声

        # 执行预测
        self.kf.predict()

        self.time_since_update += 1
        self.age += 1
        self.last_time = current_time
        pred = self.kf.x[:3].flatten()
        self.prediction_history.append(pred)
        return pred
    
    def update(self, point):
        """量测更新（固定匀加速模型）"""
        # 使用观测到的位置更新滤波器
        self.kf.update(point[:3].reshape(-1, 1))

        self.time_since_update = 0
        self.hit_streak += 1
        self.history.append(point[:3])

        # 更新时间戳
        self.last_time = point[3] if len(point) > 3 else self.last_time
    
    def assign_id(self):
        if self.id is None:
            self.id = ImprovedKalmanTracker.id_counter
            ImprovedKalmanTracker.id_counter += 1

class RadarTracker:
    def __init__(self, scan_period=6, beam_width=3):
        """雷达专用跟踪器，采用稀疏数据专用策略"""
        self.scan_period = scan_period
        self.beam_width = beam_width
        self.frame_interval = scan_period / (360/beam_width)  # 每帧时间间隔

        # 稀疏数据专用策略：基于目标ID的直接关联
        self.base_distance_threshold = 500  # 极大的关联阈值 - 稀疏数据特点
        self.max_age = 1                    # 极短生存周期 - 避免累积误差
        self.reid_age = 3                   # 短重新关联周期
        self.min_hits = 1                   # 单次命中即确认

        # 启用基于位置预测的智能关联
        self.use_position_prediction = True
        self.prediction_window = 10.0       # 预测时间窗口(秒)

        self.trackers = []
        self.suspended_trackers = []
        self.frame_count = 0
        self.beam_angle = 0  # 当前波束角度
        self.frame_buffer = defaultdict(list)  # 按波束角度缓存数据

        # 用于稀疏数据的全局轨迹记录
        self.global_tracks = {}  # 记录所有历史轨迹点
    
    def update(self, detections, beam_angle=None):
        """更新跟踪器，针对真实数据优化处理策略"""
        self.frame_count += 1
        self.beam_angle = beam_angle if beam_angle is not None else (self.beam_angle + self.beam_width) % 360

        # 对于真实数据，直接处理每帧数据（不缓存）
        # 这样可以更及时地响应目标变化
        if len(detections) > 0:
            return self._process_frame(detections)
        else:
            # 即使没有检测，也要预测现有跟踪器
            return self._process_frame(np.empty((0, 4)))
    
    def _process_frame(self, detections):
        """处理单帧数据（针对真实数据优化）"""
        # 计算当前时间
        current_time = np.max(detections[:, 3]) if len(detections) > 0 else self.frame_count * 0.05

        # 预测现有跟踪器
        preds = np.array([trk.predict(current_time) for trk in self.trackers])

        # 数据关联（马氏距离+动态阈值）
        matched, unmatched_dets, unmatched_trks = self._match(detections, preds)

        # 更新匹配的跟踪器
        for d_idx, t_idx in matched:
            self.trackers[t_idx].update(detections[d_idx])

        # 管理跟踪器生命周期
        self._manage_trackers(detections, unmatched_dets, unmatched_trks, current_time)

        # 返回确认的跟踪结果
        return self._get_confirmed_tracks()
    
    def _match(self, detections, predictions):
        """改进的数据关联（马氏距离+动态阈值）"""
        if len(predictions) == 0 or len(detections) == 0:
            return [], np.arange(len(detections)), np.arange(len(predictions))

        # 计算马氏距离矩阵
        dist_matrix = np.full((len(predictions), len(detections)), np.inf)
        valid_matches = False

        for t, (trk, pred) in enumerate(zip(self.trackers, predictions)):
            if len(detections) == 0:
                continue

            S = trk.kf.S if hasattr(trk.kf, 'S') else trk.kf.P[:3,:3] + trk.kf.R
            try:
                inv_S = np.linalg.inv(S)
            except np.linalg.LinAlgError:
                # 使用欧几里得距离作为备选
                delta = detections[:, :3] - pred
                euclidean_dist = np.linalg.norm(delta, axis=1)
                threshold = self.base_distance_threshold
                dist_matrix[t] = np.where(euclidean_dist < threshold, euclidean_dist, np.inf)
                if np.any(euclidean_dist < threshold):
                    valid_matches = True
                continue

            delta = detections[:, :3] - pred
            mahalanobis_dist = np.sqrt(np.einsum('ij,ij->i', delta @ inv_S, delta))

            # 稀疏数据专用：使用固定大阈值，简化关联逻辑
            threshold = self.base_distance_threshold

            dist_matrix[t] = np.where(mahalanobis_dist < threshold, mahalanobis_dist, np.inf)
            if np.any(mahalanobis_dist < threshold):
                valid_matches = True

        # 如果没有有效匹配，返回空匹配
        if not valid_matches or np.all(dist_matrix == np.inf):
            return [], list(range(len(detections))), list(range(len(predictions)))

        # 匈牙利算法匹配
        try:
            trk_idx, det_idx = linear_sum_assignment(dist_matrix)
            matched = [(d, t) for t, d in zip(trk_idx, det_idx) if dist_matrix[t, d] < np.inf]
        except ValueError:
            # 如果匈牙利算法失败，使用贪心匹配
            matched = []
            used_dets = set()
            used_trks = set()

            # 找到所有有效的匹配对
            valid_pairs = []
            for t in range(len(predictions)):
                for d in range(len(detections)):
                    if dist_matrix[t, d] < np.inf:
                        valid_pairs.append((dist_matrix[t, d], d, t))

            # 按距离排序，贪心选择
            valid_pairs.sort()
            for dist, d, t in valid_pairs:
                if d not in used_dets and t not in used_trks:
                    matched.append((d, t))
                    used_dets.add(d)
                    used_trks.add(t)

        # 找出未匹配项
        unmatched_dets = set(range(len(detections))) - {d for d, _ in matched}
        unmatched_trks = set(range(len(predictions))) - {t for _, t in matched}

        return matched, list(unmatched_dets), list(unmatched_trks)
    
    def _manage_trackers(self, detections, unmatched_dets, unmatched_trks, current_time):
        # 注意：unmatched_trks参数保留用于未来扩展
        """管理跟踪器生命周期"""
        # 移除过期跟踪器
        self.trackers = [t for t in self.trackers 
                        if t.time_since_update <= self.max_age or t.id is None]
        
        # 转移短暂失活的跟踪器到暂挂列表
        new_suspended = [t for t in self.trackers 
                        if self.max_age < t.time_since_update <= self.reid_age]
        self.suspended_trackers.extend(new_suspended)
        self.trackers = [t for t in self.trackers 
                        if t.time_since_update <= self.max_age]
        
        # 尝试重新关联暂挂跟踪器
        if len(unmatched_dets) > 0 and len(self.suspended_trackers) > 0:
            self._reid_unmatched(detections, unmatched_dets, current_time)
        
        # 为剩余未匹配检测创建新跟踪器
        for d_idx in unmatched_dets:
            if len(detections[d_idx]) >= 4:  # 确保数据有效
                self.trackers.append(ImprovedKalmanTracker(detections[d_idx]))
    
    def _reid_unmatched(self, detections, unmatched_dets, current_time):
        """重新关联暂挂跟踪器"""
        suspended_preds = []
        valid_suspended = []
        
        # 预测暂挂跟踪器当前位置
        for trk in self.suspended_trackers:
            pred = trk.predict(current_time)
            suspended_preds.append(pred)
            valid_suspended.append(trk)
        
        if not suspended_preds:
            return
            
        suspended_preds = np.array(suspended_preds)
        dets = detections[unmatched_dets]
        
        # 计算距离矩阵（考虑速度方向一致性）
        dists = np.linalg.norm(suspended_preds[:, None] - dets[:, :3], axis=2)
        vel_costs = np.zeros_like(dists)
        
        for i, trk in enumerate(valid_suspended):
            if hasattr(trk.kf, 'x') and len(trk.kf.x) >= 6:
                trk_vel = trk.kf.x[3:6].flatten()
                for j, det in enumerate(dets):
                    if len(trk.history) >= 2:
                        est_vel = det[:3] - trk.history[-1]
                        vel_costs[i,j] = 1 - np.dot(trk_vel, est_vel)/(np.linalg.norm(trk_vel)*np.linalg.norm(est_vel)+1e-6)
        
        # 综合距离和速度一致性
        cost_matrix = dists + vel_costs * 20
        t_idx, d_idx = linear_sum_assignment(cost_matrix)
        
        # 筛选有效匹配
        reid_matched = []
        for t, d in zip(t_idx, d_idx):
            if cost_matrix[t,d] < self.base_distance_threshold * 3:  # 放宽阈值
                reid_matched.append((d, valid_suspended[t]))
        
        # 恢复匹配的跟踪器
        reid_dets = set(range(len(unmatched_dets))) - {d for d, _ in reid_matched}
        for d, trk in reid_matched:
            trk.update(dets[d])
            trk.time_since_update = 0
            trk.hit_streak += 1
            self.trackers.append(trk)
            self.suspended_trackers.remove(trk)
        
        return list(reid_dets)
    
    def _get_confirmed_tracks(self):
        """获取确认的跟踪目标"""
        confirmed = []
        for trk in self.trackers:
            if trk.time_since_update == 0 and trk.hit_streak >= self.min_hits:
                if trk.id is None:
                    trk.assign_id()
                x = trk.kf.x[:3].flatten()
                confirmed.append(np.concatenate([x, [trk.id]]))
        return np.array(confirmed) if confirmed else np.empty((0, 4))

# ---------- 跟踪性能评估 ----------
def calculate_tracking_metrics(frames, tracked_history, association_threshold=50.0):
    """
    计算跟踪性能指标

    参数:
    - frames: 真实数据帧列表
    - tracked_history: 跟踪结果历史
    - association_threshold: 关联阈值(米)

    返回:
    - metrics: 包含各种性能指标的字典
    """
    # 提取真实轨迹
    true_trajectories = {}
    for frame_idx, frame in enumerate(frames):
        if len(frame) > 0:
            for point in frame:
                if len(point) >= 5 and point[4] != -1:  # 真实目标
                    tid = int(point[4])
                    if tid not in true_trajectories:
                        true_trajectories[tid] = []
                    true_trajectories[tid].append({
                        'frame': frame_idx,
                        'pos': point[:3],
                        'time': point[3]
                    })

    # 提取跟踪轨迹
    track_trajectories = {}
    for frame_idx, tracked in enumerate(tracked_history):
        if len(tracked) > 0:
            for track in tracked:
                tid = int(track[3])
                if tid not in track_trajectories:
                    track_trajectories[tid] = []
                track_trajectories[tid].append({
                    'frame': frame_idx,
                    'pos': track[:3],
                    'time': frame_idx * 0.05  # 假设帧间隔0.05秒
                })

    # 计算关联矩阵和性能指标
    total_true_detections = 0
    total_tracked_detections = len(tracked_history)
    correctly_tracked = 0
    false_positives = 0
    missed_detections = 0
    position_errors = []

    # 逐帧分析
    for frame_idx in range(len(frames)):
        # 获取该帧的真实目标
        true_frame = {}
        if frame_idx < len(frames) and len(frames[frame_idx]) > 0:
            for point in frames[frame_idx]:
                if len(point) >= 5 and point[4] != -1:
                    true_frame[int(point[4])] = point[:3]

        total_true_detections += len(true_frame)

        # 获取该帧的跟踪结果
        tracked_frame = {}
        if frame_idx < len(tracked_history) and len(tracked_history[frame_idx]) > 0:
            for track in tracked_history[frame_idx]:
                tracked_frame[int(track[3])] = track[:3]

        # 计算关联
        used_tracks = set()
        for true_id, true_pos in true_frame.items():
            best_match = None
            best_distance = float('inf')

            for track_id, track_pos in tracked_frame.items():
                if track_id in used_tracks:
                    continue
                distance = np.linalg.norm(true_pos - track_pos)
                if distance < best_distance and distance < association_threshold:
                    best_distance = distance
                    best_match = track_id

            if best_match is not None:
                correctly_tracked += 1
                position_errors.append(best_distance)
                used_tracks.add(best_match)
            else:
                missed_detections += 1

        # 计算假阳性
        false_positives += len(tracked_frame) - len(used_tracks)

    # 计算性能指标
    precision = correctly_tracked / (correctly_tracked + false_positives) if (correctly_tracked + false_positives) > 0 else 0
    recall = correctly_tracked / total_true_detections if total_true_detections > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    # MOTA (Multiple Object Tracking Accuracy)
    mota = 1 - (missed_detections + false_positives) / total_true_detections if total_true_detections > 0 else 0

    # MOTP (Multiple Object Tracking Precision)
    motp = np.mean(position_errors) if position_errors else float('inf')

    metrics = {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'mota': mota,
        'motp': motp,
        'total_true_detections': total_true_detections,
        'correctly_tracked': correctly_tracked,
        'false_positives': false_positives,
        'missed_detections': missed_detections,
        'avg_position_error': motp,
        'true_trajectories': true_trajectories,
        'track_trajectories': track_trajectories
    }

    return metrics

def print_tracking_metrics(metrics):
    """打印跟踪性能指标"""
    print("\n" + "="*50)
    print("跟踪性能评估结果")
    print("="*50)
    print(f"精确度 (Precision): {metrics['precision']:.3f}")
    print(f"召回率 (Recall): {metrics['recall']:.3f}")
    print(f"F1分数: {metrics['f1_score']:.3f}")
    print(f"MOTA (多目标跟踪精度): {metrics['mota']:.3f}")
    print(f"MOTP (多目标跟踪准确度): {metrics['motp']:.2f} 米")
    print(f"平均位置误差: {metrics['avg_position_error']:.2f} 米")
    print("-"*50)
    print(f"总真实检测数: {metrics['total_true_detections']}")
    print(f"正确跟踪数: {metrics['correctly_tracked']}")
    print(f"漏检数: {metrics['missed_detections']}")
    print(f"误检数: {metrics['false_positives']}")
    print("-"*50)
    print(f"真实轨迹数: {len(metrics['true_trajectories'])}")
    print(f"跟踪轨迹数: {len(metrics['track_trajectories'])}")
    print("="*50)

# ---------- 增强可视化 ----------
def visualize_tracking_comparison(frames, tracked_history, metrics=None, save=False):
    """可视化跟踪效果对比：真实轨迹 vs 跟踪轨迹"""
    fig = plt.figure(figsize=(16, 10))

    # 创建子图
    ax1 = fig.add_subplot(221, projection='3d')  # 3D总览
    ax2 = fig.add_subplot(222)                   # XY平面
    ax3 = fig.add_subplot(223)                   # 跟踪统计
    ax4 = fig.add_subplot(224)                   # 误差分析

    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    target_colors = {}
    track_colors = {}

    # 提取所有真实目标轨迹
    true_trajectories = {}
    for frame_idx, frame in enumerate(frames):
        if len(frame) > 0:
            for point in frame:
                if len(point) >= 5 and point[4] != -1:  # 真实目标
                    tid = int(point[4])
                    if tid not in true_trajectories:
                        true_trajectories[tid] = []
                        target_colors[tid] = colors[tid % len(colors)]
                    true_trajectories[tid].append({
                        'frame': frame_idx,
                        'pos': point[:3],
                        'time': point[3] if len(point) > 3 else frame_idx * 0.05
                    })

    # 提取所有跟踪轨迹
    track_trajectories = {}
    for frame_idx, tracked in enumerate(tracked_history):
        if len(tracked) > 0:
            for track in tracked:
                tid = int(track[3])
                if tid not in track_trajectories:
                    track_trajectories[tid] = []
                    track_colors[tid] = colors[tid % len(colors)]
                track_trajectories[tid].append({
                    'frame': frame_idx,
                    'pos': track[:3],
                    'time': frame_idx * 0.05
                })

    return fig, (ax1, ax2, ax3, ax4), true_trajectories, track_trajectories, target_colors, track_colors

def visualize(frames, tracked_history, save=False):
    """增强的可视化函数 - 包含性能评估"""
    # 计算性能指标
    metrics = calculate_tracking_metrics(frames, tracked_history)

    fig, axes, true_traj, track_traj, target_colors, track_colors = visualize_tracking_comparison(frames, tracked_history, metrics, save)
    ax1, ax2, ax3, ax4 = axes

    # 绘制3D总览
    ax1.set_title('3D Tracking Overview')
    ax1.set_xlim(-800, 800)
    ax1.set_ylim(-800, 800)
    ax1.set_zlim(-100, 100)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')

    # 绘制真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7,
                    label=f'True Target {tid}')

    # 绘制跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9,
                    label=f'Tracked {tid}')

    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制XY平面视图
    ax2.set_title('XY Plane View')
    ax2.set_xlim(-800, 800)
    ax2.set_ylim(-800, 800)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')

    # XY平面真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7)
            # 标记起点和终点
            ax2.scatter(positions[0,0], positions[0,1],
                       color=target_colors[tid], marker='o', s=50, alpha=0.8)
            ax2.scatter(positions[-1,0], positions[-1,1],
                       color=target_colors[tid], marker='s', s=50, alpha=0.8)

    # XY平面跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9)

    # 跟踪统计和性能指标
    ax3.set_title('Tracking Performance Metrics')

    # 使用传入的metrics或计算新的metrics
    if metrics is None:
        # 计算基本统计
        total_true_targets = len(true_traj)
        total_tracks = len(track_traj)

        # 计算每个目标的跟踪覆盖率
        coverage_stats = []
        for tid in true_traj.keys():
            true_frames = len(true_traj[tid])
            tracked_frames = len(track_traj.get(tid, []))
            coverage = tracked_frames / true_frames if true_frames > 0 else 0
            coverage_stats.append(coverage)

        avg_coverage = np.mean(coverage_stats) if coverage_stats else 0

        stats_text = f"""基本统计:
真实目标数: {total_true_targets}
跟踪轨迹数: {total_tracks}
平均覆盖率: {avg_coverage:.2%}

目标覆盖率:"""

        for tid, coverage in zip(true_traj.keys(), coverage_stats):
            stats_text += f"\n目标 {tid}: {coverage:.2%}"
    else:
        # 显示详细的性能指标
        stats_text = f"""性能指标:
精确度: {metrics['precision']:.3f}
召回率: {metrics['recall']:.3f}
F1分数: {metrics['f1_score']:.3f}
MOTA: {metrics['mota']:.3f}
MOTP: {metrics['motp']:.1f}m

统计信息:
真实检测: {metrics['total_true_detections']}
正确跟踪: {metrics['correctly_tracked']}
漏检: {metrics['missed_detections']}
误检: {metrics['false_positives']}

轨迹数量:
真实轨迹: {len(metrics['true_trajectories'])}
跟踪轨迹: {len(metrics['track_trajectories'])}"""

    ax3.text(0.05, 0.95, stats_text, transform=ax3.transAxes,
             verticalalignment='top', fontfamily='monospace', fontsize=9)
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')

    # 误差分析
    ax4.set_title('Tracking Error Analysis')

    # 计算跟踪误差（当真实目标和跟踪目标都存在时）
    frame_errors = []
    for frame_idx in range(len(frames)):
        frame_error = 0
        error_count = 0

        # 获取该帧的真实目标
        true_targets_frame = {}
        if frame_idx < len(frames) and len(frames[frame_idx]) > 0:
            for point in frames[frame_idx]:
                if len(point) >= 5 and point[4] != -1:
                    true_targets_frame[int(point[4])] = point[:3]

        # 获取该帧的跟踪结果
        tracked_frame = {}
        if frame_idx < len(tracked_history) and len(tracked_history[frame_idx]) > 0:
            for track in tracked_history[frame_idx]:
                tracked_frame[int(track[3])] = track[:3]

        # 计算匹配目标的误差
        for tid in true_targets_frame.keys():
            if tid in tracked_frame:
                error = np.linalg.norm(true_targets_frame[tid] - tracked_frame[tid])
                frame_error += error
                error_count += 1

        avg_frame_error = frame_error / error_count if error_count > 0 else 0
        frame_errors.append(avg_frame_error)

    ax4.plot(frame_errors, 'b-', alpha=0.7, linewidth=1)
    ax4.set_xlabel('Frame')
    ax4.set_ylabel('Average Error (m)')
    ax4.grid(True, alpha=0.3)

    if frame_errors:
        ax4.set_ylim(0, max(max(frame_errors), 10))
        avg_error = np.mean([e for e in frame_errors if e > 0])
        ax4.axhline(y=avg_error, color='r', linestyle='--', alpha=0.7,
                   label=f'Avg Error: {avg_error:.1f}m')
        ax4.legend()

    plt.tight_layout()

    if save:
        plt.savefig('tracking_comparison.png', dpi=150, bbox_inches='tight')
        print("跟踪对比图已保存到: tracking_comparison.png")

    plt.show()
    return fig

# ---------- 主测试流程 ----------
def main_test():
    """主测试函数 - 使用CSV数据进行跟踪测试"""
    print("开始雷达目标跟踪测试...")

    # 从CSV文件读取雷达数据
    frames, frame_info = load_radar_data_from_csv('/home/<USER>/My_Project/MSHNet_TensorRT_Infer/python/radar_simulation_data_1.csv')

    if not frames:
        print("无法加载数据，测试终止")
        return

    # 初始化跟踪器（根据数据特点调整参数）
    tracker = RadarTracker(scan_period=6, beam_width=3)
    tracked_history = []

    print(f"\n开始处理 {len(frames)} 帧数据...")

    # 处理每帧数据
    for frame_idx, frame in enumerate(frames):
        # 获取当前帧信息
        current_frame_info = frame_info.get(frame_idx, {})
        beam_angle = current_frame_info.get('beam_angle', (frame_idx * 3) % 360)

        # 聚类检测点（针对真实数据调整参数）
        clustered = cluster_detections(frame, eps=0.001, min_samples=1)

        # 更新跟踪器
        tracked = tracker.update(clustered, beam_angle=beam_angle)
        tracked_history.append(tracked)

        # 每10帧打印一次状态
        if frame_idx % 10 == 0 or frame_idx == len(frames) - 1:
            print(f"Frame {frame_idx}: Beam {beam_angle:.1f}°, "
                  f"Detections {len(clustered)}, Tracks {len(tracked)}, "
                  f"True targets: {current_frame_info.get('num_targets', 0)}")

    print("\n数据处理完成，开始性能评估...")

    # 计算跟踪性能指标
    metrics = calculate_tracking_metrics(frames, tracked_history)
    print_tracking_metrics(metrics)

    # 可视化跟踪效果对比
    print("\n生成可视化图表...")
    visualize(frames, tracked_history, save=True)

    return frames, tracked_history, metrics

if __name__ == '__main__':
    main_test()