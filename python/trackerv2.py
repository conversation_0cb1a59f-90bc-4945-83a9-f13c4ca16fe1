import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from filterpy.kalman import <PERSON><PERSON><PERSON><PERSON>er, IMMEstimator
from scipy.optimize import linear_sum_assignment
from collections import defaultdict
import warnings
warnings.filterwarnings("ignore", category=np.VisibleDeprecationWarning)

# ---------- 数据生成与聚类 ----------
def generate_synthetic_radar_data(num_frames=120, num_targets=5, scan_period=6.0, beam_width=3.0,
                                 fps=20, noise_std=3.0, detection_range=1000):
    """
    生成360度扫描雷达模拟数据

    参数:
    - num_frames: 总帧数 (默认120帧 = 6秒 * 20fps)
    - num_targets: 目标数量
    - scan_period: 扫描周期(秒) - 6秒旋转一圈
    - beam_width: 波束宽度(度) - 每帧3度数据
    - fps: 帧率 - 一秒20帧
    - noise_std: 噪声标准差
    - detection_range: 雷达探测距离(米)
    """
    data = []
    np.random.seed(42)

    # 初始化目标状态（确保在雷达检测范围内）
    base_positions = np.random.uniform(-300, 300, size=(num_targets, 3))  # 减小初始范围
    base_velocities = np.random.uniform(-8, 8, size=(num_targets, 3))  # 减小速度避免快速离开

    # 计算时间参数
    frame_time_interval = 1.0 / fps  # 每帧时间间隔 = 0.05秒
    degrees_per_frame = 360.0 / (scan_period * fps)  # 每帧扫描角度 = 3度

    print(f"雷达参数:")
    print(f"  扫描周期: {scan_period}秒")
    print(f"  帧率: {fps}fps")
    print(f"  每帧时间间隔: {frame_time_interval:.3f}秒")
    print(f"  每帧扫描角度: {degrees_per_frame:.1f}度")
    print(f"  总帧数: {num_frames}")

    for frame in range(num_frames):
        current_time = frame * frame_time_interval
        current_beam_angle = (frame * degrees_per_frame) % 360.0  # 当前波束角度
        points = []

        for i in range(num_targets):
            # 目标运动模型 - 匀速直线运动 + 轻微机动
            maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
            pos = (base_positions[i] +
                   base_velocities[i] * current_time +
                   maneuver * current_time)

            # 计算目标相对雷达的方位角
            target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
            target_range = np.linalg.norm(pos[:2])  # 水平距离

            # 雷达波束覆盖检查
            beam_start = current_beam_angle
            beam_end = (current_beam_angle + beam_width) % 360

            # 处理跨越0度的情况
            if beam_end < beam_start:  # 跨越0度
                in_beam = (target_azimuth >= beam_start) or (target_azimuth <= beam_end)
            else:
                in_beam = beam_start <= target_azimuth <= beam_end

            # 距离检查
            in_range = target_range <= detection_range

            # 高检测概率模型（用于测试）
            if in_beam and in_range:
                # 非常高的基础检测概率
                base_prob = 0.99

                # 很小的距离衰减
                range_factor = max(0.9, 1.0 - target_range / detection_range * 0.1)

                # 很小的波束边缘衰减
                beam_center = (beam_start + beam_width/2) % 360
                angle_diff = min(abs(target_azimuth - beam_center),
                               360 - abs(target_azimuth - beam_center))
                beam_factor = max(0.95, 1.0 - angle_diff / (beam_width/2 + 1.0))  # 增加1度容差

                detection_prob = base_prob * range_factor * beam_factor

                if np.random.rand() < detection_prob:
                    # 添加测量噪声
                    noise = np.random.normal(0, noise_std, size=3)
                    # 距离噪声与距离成正比
                    range_noise_factor = 1 + target_range / 1000.0
                    noise[:2] *= range_noise_factor

                    noisy_pos = pos + noise
                    points.append(np.hstack((noisy_pos, current_time, i)))

        # 添加杂波 - 只在当前波束扇区内（适中密度）
        clutter_density = 0.3  # 每度杂波密度 - 适中
        expected_clutter = int(beam_width * clutter_density)
        num_clutter = np.random.poisson(expected_clutter)

        for _ in range(num_clutter):
            # 在当前波束扇区内随机生成杂波
            clutter_azimuth = np.random.uniform(current_beam_angle,
                                              current_beam_angle + beam_width) % 360
            clutter_range = np.random.uniform(50, detection_range)
            clutter_elevation = np.random.uniform(-50, 50)

            # 转换为笛卡尔坐标
            clutter_x = clutter_range * np.cos(np.radians(clutter_azimuth))
            clutter_y = clutter_range * np.sin(np.radians(clutter_azimuth))
            clutter_z = clutter_elevation

            clutter_pos = np.array([clutter_x, clutter_y, clutter_z])
            points.append(np.hstack((clutter_pos, current_time, -1)))  # -1表示杂波

        data.append(np.array(points) if points else np.empty((0, 5)))

        # 每30帧打印一次进度和调试信息
        if frame % 30 == 0:
            target_info = []
            for i in range(num_targets):
                current_time = frame * frame_time_interval
                maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
                pos = (base_positions[i] +
                       base_velocities[i] * current_time +
                       maneuver * current_time)
                target_range = np.linalg.norm(pos[:2])
                target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
                target_info.append(f"T{i}:r={target_range:.0f}m,az={target_azimuth:.0f}°")

            print(f"生成帧 {frame}/{num_frames}, 波束角度: {current_beam_angle:.1f}°, "
                  f"检测点数: {len(points)}")
            print(f"  目标状态: {' '.join(target_info)}")

    return data

def cluster_detections(points, eps=0.5, min_samples=1):
    """DBSCAN聚类检测点"""
    if len(points) == 0:
        return np.empty((0, 5))
    
    # 标准化坐标后聚类
    scaled = StandardScaler().fit_transform(points[:, :3])
    labels = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(scaled)
    
    # 合并聚类点
    clustered = []
    for lbl in np.unique(labels):
        if lbl == -1:  # 噪声点跳过
            continue
        cluster = points[labels == lbl]
        centroid = np.mean(cluster[:, :3], axis=0)
        timestamp = np.min(cluster[:, 3])  # 取最早时间戳
        clustered.append(np.hstack((centroid, timestamp)))
    
    return np.array(clustered) if clustered else np.empty((0, 4))

# ---------- 改进的跟踪器 ----------
class ImprovedKalmanTracker:
    id_counter = 0
    
    def __init__(self, point, use_imm=False):
        """初始化跟踪器，支持匀加速模型和IMM"""
        self.use_imm = use_imm
        self.last_time = point[3]
        
        if use_imm:
            # IMM初始化（需安装filterpy）
            try:
                self.kf = IMMEstimator(
                    filters=[self._create_cv_filter(), self._create_ca_filter()],
                    mu=[0.5, 0.5],
                    M=[[0.9, 0.1], [0.1, 0.9]]  # 模型转移概率
                )
            except ImportError:
                print("Warning: IMMEstimator not available, falling back to CA model")
                self.use_imm = False
                self.kf = KalmanFilter(dim_x=9, dim_z=3)
                self._init_ca_filter()
        else:
            # 匀加速模型（9维状态）
            self.kf = KalmanFilter(dim_x=9, dim_z=3)
            self._init_ca_filter()
        
        self.kf.x[:3] = point[:3].reshape(-1, 1)
        self.id = None
        self.time_since_update = 0
        self.hit_streak = 1
        self.age = 0
        self.history = []
        self.prediction_history = []
    
    def _init_ca_filter(self):
        """初始化匀加速模型滤波器"""
        dt = 1.0  # 初始值，后续动态更新
        self.kf.F = np.array([
            [1,0,0, dt,0,0, 0.5*dt**2,0,0],
            [0,1,0, 0,dt,0, 0,0.5*dt**2,0],
            [0,0,1, 0,0,dt, 0,0,0.5*dt**2],
            [0,0,0, 1,0,0, dt,0,0],
            [0,0,0, 0,1,0, 0,dt,0],
            [0,0,0, 0,0,1, 0,0,dt],
            [0,0,0, 0,0,0, 1,0,0],
            [0,0,0, 0,0,0, 0,1,0],
            [0,0,0, 0,0,0, 0,0,1]
        ])
        self.kf.H = np.eye(3, 9)
        self.kf.R = np.eye(3) * 10  # 观测噪声
        self.kf.P = np.eye(9) * 100  # 初始协方差
        # 过程噪声（加速度噪声较大）
        self.kf.Q = np.diag([0.1, 0.1, 0.1, 1, 1, 1, 10, 10, 10])  
    
    def _create_cv_filter(self):
        """创建匀速模型滤波器（用于IMM）"""
        kf = KalmanFilter(dim_x=6, dim_z=3)
        kf.F = np.eye(6)
        kf.F[:3, 3:6] = np.eye(3)
        kf.H = np.eye(3, 6)
        kf.R = np.eye(3) * 10
        kf.P = np.eye(6) * 100
        kf.Q = np.diag([0.1, 0.1, 0.1, 1, 1, 1])
        return kf
    
    def _create_ca_filter(self):
        """创建匀加速模型滤波器（用于IMM）"""
        kf = KalmanFilter(dim_x=9, dim_z=3)
        self._init_ca_filter()
        return kf
    
    def predict(self, current_time):
        """动态时间步长预测"""
        dt = max(0.1, current_time - self.last_time)  # 最小0.1秒防止数值问题
        
        if self.use_imm:
            self.kf.predict(dt=dt)
        else:
            # 更新状态转移矩阵和过程噪声
            self.kf.F[0,3] = dt
            self.kf.F[1,4] = dt
            self.kf.F[2,5] = dt
            self.kf.F[0,6] = 0.5*dt**2
            self.kf.F[1,7] = 0.5*dt**2
            self.kf.F[2,8] = 0.5*dt**2
            self.kf.F[3,6] = dt
            self.kf.F[4,7] = dt
            self.kf.F[5,8] = dt
            
            # 过程噪声随dt增长
            self.kf.Q[:3,:3] = np.eye(3) * (0.1 * dt)
            self.kf.Q[3:6,3:6] = np.eye(3) * (1 * dt)
            self.kf.Q[6:,6:] = np.eye(3) * (10 * dt**2)
            
            self.kf.predict()
        
        self.time_since_update += 1
        self.age += 1
        self.last_time = current_time
        pred = self.kf.x[:3].flatten()
        self.prediction_history.append(pred)
        return pred
    
    def update(self, point):
        """量测更新"""
        if self.use_imm:
            self.kf.update(point[:3].reshape(-1, 1))
        else:
            self.kf.update(point[:3].reshape(-1, 1))
        
        self.time_since_update = 0
        self.hit_streak += 1
        self.history.append(point[:3])
    
    def assign_id(self):
        if self.id is None:
            self.id = ImprovedKalmanTracker.id_counter
            ImprovedKalmanTracker.id_counter += 1

class RadarTracker:
    def __init__(self, scan_period=6, beam_width=3):
        """雷达专用跟踪器，考虑扫描特性"""
        self.scan_period = scan_period
        self.beam_width = beam_width
        self.frame_interval = scan_period / (360/beam_width)  # 每帧时间间隔
        
        # 动态参数（进一步优化跟踪连续性）
        self.base_distance_threshold = 120  # 基础关联阈值(m) - 进一步放宽
        self.max_age = int(4 * scan_period / self.frame_interval)  # 4个扫描周期 - 进一步延长
        self.reid_age = int(8 * scan_period / self.frame_interval)  # 8个扫描周期 - 进一步延长
        self.min_hits = 1  # 最小确认命中次数 - 单次命中即确认
        
        self.trackers = []
        self.suspended_trackers = []
        self.frame_count = 0
        self.beam_angle = 0  # 当前波束角度
        self.frame_buffer = defaultdict(list)  # 按波束角度缓存数据
    
    def update(self, detections, beam_angle=None):
        """更新跟踪器，支持分帧处理"""
        self.frame_count += 1
        self.beam_angle = beam_angle if beam_angle is not None else (self.beam_angle + self.beam_width) % 360
        
        # 缓存当前波束数据
        if len(detections) > 0:
            self.frame_buffer[beam_angle].extend(detections)
        
        # 每10度处理一次（进一步提高跟踪连续性）
        if self.beam_angle % 10 == 0:
            all_dets = np.concatenate([np.array(buf) for buf in self.frame_buffer.values()]) if self.frame_buffer else np.empty((0, 4))
            self.frame_buffer.clear()
            return self._process_full_scan(all_dets)
        return np.empty((0, 4))
    
    def _process_full_scan(self, detections):
        # 预测现有跟踪器
        current_time = np.max(detections[:, 3]) if len(detections) > 0 else self.frame_count * self.frame_interval
        preds = np.array([trk.predict(current_time) for trk in self.trackers])
        
        # 数据关联（马氏距离+动态阈值）
        matched, unmatched_dets, unmatched_trks = self._match(detections, preds)
        
        # 更新匹配的跟踪器
        for d_idx, t_idx in matched:
            self.trackers[t_idx].update(detections[d_idx])
        
        # 管理跟踪器生命周期
        self._manage_trackers(detections, unmatched_dets, unmatched_trks, current_time)
        
        # 返回确认的跟踪结果
        return self._get_confirmed_tracks()
    
    def _match(self, detections, predictions):
        """改进的数据关联（马氏距离+动态阈值）"""
        if len(predictions) == 0 or len(detections) == 0:
            return [], np.arange(len(detections)), np.arange(len(predictions))

        # 计算马氏距离矩阵
        dist_matrix = np.full((len(predictions), len(detections)), np.inf)
        valid_matches = False

        for t, (trk, pred) in enumerate(zip(self.trackers, predictions)):
            if len(detections) == 0:
                continue

            S = trk.kf.S if hasattr(trk.kf, 'S') else trk.kf.P[:3,:3] + trk.kf.R
            try:
                inv_S = np.linalg.inv(S)
            except np.linalg.LinAlgError:
                # 使用欧几里得距离作为备选
                delta = detections[:, :3] - pred
                euclidean_dist = np.linalg.norm(delta, axis=1)
                threshold = self.base_distance_threshold
                dist_matrix[t] = np.where(euclidean_dist < threshold, euclidean_dist, np.inf)
                if np.any(euclidean_dist < threshold):
                    valid_matches = True
                continue

            delta = detections[:, :3] - pred
            mahalanobis_dist = np.sqrt(np.einsum('ij,ij->i', delta @ inv_S, delta))

            # 动态阈值：基于目标速度调整
            velocity = np.linalg.norm(trk.kf.x[3:6]) if hasattr(trk.kf, 'x') and len(trk.kf.x) >= 6 else 0
            threshold = self.base_distance_threshold * (1 + velocity * 0.02)
            dist_matrix[t] = np.where(mahalanobis_dist < threshold, mahalanobis_dist, np.inf)
            if np.any(mahalanobis_dist < threshold):
                valid_matches = True

        # 如果没有有效匹配，返回空匹配
        if not valid_matches or np.all(dist_matrix == np.inf):
            return [], list(range(len(detections))), list(range(len(predictions)))

        # 匈牙利算法匹配
        try:
            trk_idx, det_idx = linear_sum_assignment(dist_matrix)
            matched = [(d, t) for t, d in zip(trk_idx, det_idx) if dist_matrix[t, d] < np.inf]
        except ValueError:
            # 如果匈牙利算法失败，使用贪心匹配
            matched = []
            used_dets = set()
            used_trks = set()

            # 找到所有有效的匹配对
            valid_pairs = []
            for t in range(len(predictions)):
                for d in range(len(detections)):
                    if dist_matrix[t, d] < np.inf:
                        valid_pairs.append((dist_matrix[t, d], d, t))

            # 按距离排序，贪心选择
            valid_pairs.sort()
            for dist, d, t in valid_pairs:
                if d not in used_dets and t not in used_trks:
                    matched.append((d, t))
                    used_dets.add(d)
                    used_trks.add(t)

        # 找出未匹配项
        unmatched_dets = set(range(len(detections))) - {d for d, _ in matched}
        unmatched_trks = set(range(len(predictions))) - {t for _, t in matched}

        return matched, list(unmatched_dets), list(unmatched_trks)
    
    def _manage_trackers(self, detections, unmatched_dets, unmatched_trks, current_time):
        # 注意：unmatched_trks参数保留用于未来扩展
        """管理跟踪器生命周期"""
        # 移除过期跟踪器
        self.trackers = [t for t in self.trackers 
                        if t.time_since_update <= self.max_age or t.id is None]
        
        # 转移短暂失活的跟踪器到暂挂列表
        new_suspended = [t for t in self.trackers 
                        if self.max_age < t.time_since_update <= self.reid_age]
        self.suspended_trackers.extend(new_suspended)
        self.trackers = [t for t in self.trackers 
                        if t.time_since_update <= self.max_age]
        
        # 尝试重新关联暂挂跟踪器
        if len(unmatched_dets) > 0 and len(self.suspended_trackers) > 0:
            self._reid_unmatched(detections, unmatched_dets, current_time)
        
        # 为剩余未匹配检测创建新跟踪器
        for d_idx in unmatched_dets:
            if len(detections[d_idx]) >= 4:  # 确保数据有效
                self.trackers.append(ImprovedKalmanTracker(detections[d_idx]))
    
    def _reid_unmatched(self, detections, unmatched_dets, current_time):
        """重新关联暂挂跟踪器"""
        suspended_preds = []
        valid_suspended = []
        
        # 预测暂挂跟踪器当前位置
        for trk in self.suspended_trackers:
            pred = trk.predict(current_time)
            suspended_preds.append(pred)
            valid_suspended.append(trk)
        
        if not suspended_preds:
            return
            
        suspended_preds = np.array(suspended_preds)
        dets = detections[unmatched_dets]
        
        # 计算距离矩阵（考虑速度方向一致性）
        dists = np.linalg.norm(suspended_preds[:, None] - dets[:, :3], axis=2)
        vel_costs = np.zeros_like(dists)
        
        for i, trk in enumerate(valid_suspended):
            if hasattr(trk.kf, 'x') and len(trk.kf.x) >= 6:
                trk_vel = trk.kf.x[3:6].flatten()
                for j, det in enumerate(dets):
                    if len(trk.history) >= 2:
                        est_vel = det[:3] - trk.history[-1]
                        vel_costs[i,j] = 1 - np.dot(trk_vel, est_vel)/(np.linalg.norm(trk_vel)*np.linalg.norm(est_vel)+1e-6)
        
        # 综合距离和速度一致性
        cost_matrix = dists + vel_costs * 20
        t_idx, d_idx = linear_sum_assignment(cost_matrix)
        
        # 筛选有效匹配
        reid_matched = []
        for t, d in zip(t_idx, d_idx):
            if cost_matrix[t,d] < self.base_distance_threshold * 3:  # 放宽阈值
                reid_matched.append((d, valid_suspended[t]))
        
        # 恢复匹配的跟踪器
        reid_dets = set(range(len(unmatched_dets))) - {d for d, _ in reid_matched}
        for d, trk in reid_matched:
            trk.update(dets[d])
            trk.time_since_update = 0
            trk.hit_streak += 1
            self.trackers.append(trk)
            self.suspended_trackers.remove(trk)
        
        return list(reid_dets)
    
    def _get_confirmed_tracks(self):
        """获取确认的跟踪目标"""
        confirmed = []
        for trk in self.trackers:
            if trk.time_since_update == 0 and trk.hit_streak >= self.min_hits:
                if trk.id is None:
                    trk.assign_id()
                x = trk.kf.x[:3].flatten()
                confirmed.append(np.concatenate([x, [trk.id]]))
        return np.array(confirmed) if confirmed else np.empty((0, 4))

# ---------- 增强可视化 ----------
def visualize_tracking_comparison(frames, tracked_history, save=False):
    """可视化跟踪效果对比：真实轨迹 vs 跟踪轨迹"""
    fig = plt.figure(figsize=(16, 10))

    # 创建子图
    ax1 = fig.add_subplot(221, projection='3d')  # 3D总览
    ax2 = fig.add_subplot(222)                   # XY平面
    ax3 = fig.add_subplot(223)                   # 跟踪统计
    ax4 = fig.add_subplot(224)                   # 误差分析

    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    target_colors = {}
    track_colors = {}

    # 提取所有真实目标轨迹
    true_trajectories = {}
    for frame_idx, frame in enumerate(frames):
        if len(frame) > 0:
            for point in frame:
                if len(point) >= 5 and point[4] != -1:  # 真实目标
                    tid = int(point[4])
                    if tid not in true_trajectories:
                        true_trajectories[tid] = []
                        target_colors[tid] = colors[tid % len(colors)]
                    true_trajectories[tid].append({
                        'frame': frame_idx,
                        'pos': point[:3],
                        'time': point[3] if len(point) > 3 else frame_idx * 0.05
                    })

    # 提取所有跟踪轨迹
    track_trajectories = {}
    for frame_idx, tracked in enumerate(tracked_history):
        if len(tracked) > 0:
            for track in tracked:
                tid = int(track[3])
                if tid not in track_trajectories:
                    track_trajectories[tid] = []
                    track_colors[tid] = colors[tid % len(colors)]
                track_trajectories[tid].append({
                    'frame': frame_idx,
                    'pos': track[:3],
                    'time': frame_idx * 0.05
                })

    return fig, (ax1, ax2, ax3, ax4), true_trajectories, track_trajectories, target_colors, track_colors

def visualize(frames, tracked_history, save=False):
    """原始可视化函数 - 保持兼容性"""
    fig, axes, true_traj, track_traj, target_colors, track_colors = visualize_tracking_comparison(frames, tracked_history, save)
    ax1, ax2, ax3, ax4 = axes

    # 绘制3D总览
    ax1.set_title('3D Tracking Overview')
    ax1.set_xlim(-800, 800)
    ax1.set_ylim(-800, 800)
    ax1.set_zlim(-100, 100)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')

    # 绘制真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7,
                    label=f'True Target {tid}')

    # 绘制跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9,
                    label=f'Tracked {tid}')

    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制XY平面视图
    ax2.set_title('XY Plane View')
    ax2.set_xlim(-800, 800)
    ax2.set_ylim(-800, 800)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')

    # XY平面真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7)
            # 标记起点和终点
            ax2.scatter(positions[0,0], positions[0,1],
                       color=target_colors[tid], marker='o', s=50, alpha=0.8)
            ax2.scatter(positions[-1,0], positions[-1,1],
                       color=target_colors[tid], marker='s', s=50, alpha=0.8)

    # XY平面跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9)

    # 跟踪统计
    ax3.set_title('Tracking Statistics')

    # 计算跟踪统计
    total_true_targets = len(true_traj)
    total_tracks = len(track_traj)

    # 计算每个目标的跟踪覆盖率
    coverage_stats = []
    for tid in true_traj.keys():
        true_frames = len(true_traj[tid])
        tracked_frames = len(track_traj.get(tid, []))
        coverage = tracked_frames / true_frames if true_frames > 0 else 0
        coverage_stats.append(coverage)

    avg_coverage = np.mean(coverage_stats) if coverage_stats else 0

    stats_text = f"""
    True Targets: {total_true_targets}
    Tracked Objects: {total_tracks}
    Avg Coverage: {avg_coverage:.2%}

    Target Coverage:
    """

    for tid, coverage in zip(true_traj.keys(), coverage_stats):
        stats_text += f"Target {tid}: {coverage:.2%}\n"

    ax3.text(0.05, 0.95, stats_text, transform=ax3.transAxes,
             verticalalignment='top', fontfamily='monospace', fontsize=10)
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')

    # 误差分析
    ax4.set_title('Tracking Error Analysis')

    # 计算跟踪误差（当真实目标和跟踪目标都存在时）
    frame_errors = []
    for frame_idx in range(len(frames)):
        frame_error = 0
        error_count = 0

        # 获取该帧的真实目标
        true_targets_frame = {}
        if frame_idx < len(frames) and len(frames[frame_idx]) > 0:
            for point in frames[frame_idx]:
                if len(point) >= 5 and point[4] != -1:
                    true_targets_frame[int(point[4])] = point[:3]

        # 获取该帧的跟踪结果
        tracked_frame = {}
        if frame_idx < len(tracked_history) and len(tracked_history[frame_idx]) > 0:
            for track in tracked_history[frame_idx]:
                tracked_frame[int(track[3])] = track[:3]

        # 计算匹配目标的误差
        for tid in true_targets_frame.keys():
            if tid in tracked_frame:
                error = np.linalg.norm(true_targets_frame[tid] - tracked_frame[tid])
                frame_error += error
                error_count += 1

        avg_frame_error = frame_error / error_count if error_count > 0 else 0
        frame_errors.append(avg_frame_error)

    ax4.plot(frame_errors, 'b-', alpha=0.7, linewidth=1)
    ax4.set_xlabel('Frame')
    ax4.set_ylabel('Average Error (m)')
    ax4.grid(True, alpha=0.3)

    if frame_errors:
        ax4.set_ylim(0, max(max(frame_errors), 10))
        avg_error = np.mean([e for e in frame_errors if e > 0])
        ax4.axhline(y=avg_error, color='r', linestyle='--', alpha=0.7,
                   label=f'Avg Error: {avg_error:.1f}m')
        ax4.legend()

    plt.tight_layout()

    if save:
        plt.savefig('tracking_comparison.png', dpi=150, bbox_inches='tight')
        print("跟踪对比图已保存到: tracking_comparison.png")

    plt.show()
    return fig

# ---------- 主测试流程 ----------
def main_test():
    # 生成模拟数据（6秒扫描周期，3度波束宽度）
    frames = generate_synthetic_radar_data(num_frames=120, scan_period=6)
    
    # 初始化跟踪器
    tracker = RadarTracker(scan_period=6, beam_width=3)
    tracked_history = []
    
    # 处理每帧数据
    for frame_idx, frame in enumerate(frames):
        # 模拟雷达扫描角度（每帧3度）
        beam_angle = (frame_idx * 3) % 360
        
        # 聚类检测点
        clustered = cluster_detections(frame, eps=0.1, min_samples=1)
        
        # 更新跟踪器（传入当前波束角度）
        tracked = tracker.update(clustered, beam_angle=beam_angle)
        tracked_history.append(tracked)
        
        # 打印状态
        print(f"Frame {frame_idx}: Beam {beam_angle}°, Detections {len(clustered)}, Tracks {len(tracked)}")
    
    # 可视化跟踪效果对比
    print("\n=== 跟踪效果分析 ===")
    visualize(frames, tracked_history, save=True)

if __name__ == '__main__':
    main_test()