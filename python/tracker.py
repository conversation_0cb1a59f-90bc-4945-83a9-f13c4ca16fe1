import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from filterpy.kalman import <PERSON><PERSON><PERSON>ilter
from scipy.optimize import linear_sum_assignment

# ---------- 数据生成与聚类 ----------
def generate_synthetic_data(num_frames=30, num_targets=3, drop_interval=3, noise_std=3):
    data = []
    np.random.seed(34)
    base_positions = np.random.uniform(-500, 500, size=(num_targets, 3))
    base_velocities = np.random.uniform(-5, 5, size=(num_targets, 3))

    for frame in range(num_frames):
        points = []
        for i in range(num_targets):
            velocity = base_velocities[i] + 2 * np.sin(frame / 50.0 + i)
            pos = base_positions[i] + frame * velocity
            noisy = pos + np.random.normal(0, noise_std, size=3)

            if drop_interval > 0 and frame % drop_interval == 0:
                if np.random.rand() < 0.5:
                    continue

            points.append(np.hstack((noisy, frame, i)))

        num_clutter = np.random.randint(1, 5)
        for _ in range(num_clutter):
            clutter_pos = np.random.uniform(-600, 600, size=3)
            clutter = np.hstack((clutter_pos, frame, -1))
            points.append(clutter)

        data.append(np.array(points))
    return data

def cluster_detections(points, eps=0.5, min_samples=1):
    if points.shape[0] == 0:
        return points
    scaled = StandardScaler().fit_transform(points[:, :3])
    labels = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(scaled)

    result = []
    for lbl in np.unique(labels):
        cluster = points[labels == lbl]
        center = np.mean(cluster[:, :3], axis=0)
        min_time = cluster[np.argmin(cluster[:, 3]), 3]
        result.append(np.hstack((center, min_time)))
    return np.array(result)

# ---------- 跟踪器 ----------
class KalmanPointTracker:
    id_counter = 0  # 全局 ID 计数器

    def __init__(self, point):
        self.kf = KalmanFilter(dim_x=6, dim_z=3)
        dt = 1.0
        self.kf.F = np.array([
            [1, 0, 0, dt, 0, 0],
            [0, 1, 0, 0, dt, 0],
            [0, 0, 1, 0, 0, dt],
            [0, 0, 0, 1, 0, 0],
            [0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 1]
        ])
        self.kf.H = np.eye(3, 6)
        self.kf.R *= 10.0
        self.kf.P *= 10.0
        self.kf.Q = np.eye(6) * 0.1

        self.kf.x[:3] = point[:3].reshape(3, 1)

        self.id = None  # 延迟赋 ID
        self.time_since_update = 0
        self.hit_streak = 1
        self.age = 1
        self.history = []
        self.last_time = point[3]

    def assign_id(self):
        if self.id is None:
            self.id = KalmanPointTracker.id_counter
            KalmanPointTracker.id_counter += 1

    def update(self, point):
        self.kf.update(point[:3].reshape(3, 1))
        self.time_since_update = 0
        self.hit_streak += 1
        self.last_time = point[3]

    def predict(self):
        self.kf.predict()
        self.time_since_update += 1
        self.age += 1
        pred = self.kf.x[:3].flatten()
        self.history.append(pred)
        return pred

class PointTracker:
    def __init__(self, max_age=3, distance_threshold=50, reid_age=20, min_hits=2):
        self.max_age = max_age
        self.distance_threshold = distance_threshold
        self.reid_age = reid_age
        self.min_hits = min_hits
        self.trackers = []
        self.suspended_trackers = []
        self.frame_count = 0

    def update(self, detections):
        self.frame_count += 1

        preds = np.array([trk.predict() for trk in self.trackers])
        matched, unmatched_dets, unmatched_trks = self.match(detections, preds)

        # --- 更新匹配的追踪器 ---
        for m_det, m_trk in matched:
            self.trackers[m_trk].update(detections[m_det])

        # --- 清理和转移失活追踪器 ---
        new_trackers = []
        for trk in self.trackers:
            if trk.time_since_update <= self.max_age:
                # 活跃跟踪器，保留在主列表
                new_trackers.append(trk)
            elif trk.time_since_update <= self.reid_age:
                # 短暂失活，转移到暂挂列表
                self.suspended_trackers.append(trk)
            # 超过reid_age的跟踪器会被自动丢弃
        self.trackers = new_trackers # 更新主跟踪器列表

        # --- reid 匹配恢复追踪器 ---
        reid_matched, reid_dets, _ = self.reid_match(detections[unmatched_dets])
        for det_idx, old_trk in reid_matched:
            # 重置暂挂跟踪器状态
            old_trk.kf.x[:3] = detections[unmatched_dets[det_idx]][:3].reshape(3, 1) # 更新位置
            old_trk.time_since_update = 0   # 重置未更新时间
            old_trk.hit_streak = 2          # 重置连续命中计数
            old_trk.age = 1                 # 重置年龄
            old_trk.last_time = detections[unmatched_dets[det_idx]][3]
            self.trackers.append(old_trk)   # 移回主跟踪器列表

        # --- 未匹配的检测创建新追踪器 ---
        for idx in reid_dets:
            self.trackers.append(KalmanPointTracker(detections[unmatched_dets[idx]]))

        # --- 返回符合 min_hits 要求的目标 ---
        results = []
        for trk in self.trackers:
            if trk.time_since_update < 1 and (trk.hit_streak >= self.min_hits or self.frame_count <= self.min_hits):
                if trk.id is None and trk.hit_streak >= self.min_hits:
                    trk.assign_id()  # 分配 ID 只发生一次
                if trk.id is not None:
                    pred = trk.kf.x[:3].flatten()
                    results.append(np.concatenate([pred, [trk.id]]))
        return np.array(results)

    def match(self, detections, predictions):
        # 如果预测为空（无跟踪器），直接返回所有检测为未匹配
        if len(predictions) == 0:
            return [], np.arange(len(detections)), []
        
        # 计算所有预测与检测点迹之间的欧式距离
        dists = np.linalg.norm(predictions[:, None] - detections[:, :3], axis=2)
        
        # 匈牙利算法计算最优匹配
        trk_idx, det_idx = linear_sum_assignment(dists)

        matched = []
        unmatched_dets = list(range(len(detections)))
        unmatched_trks = list(range(len(predictions)))

        # 根据距离阈值筛选有效匹配
        for t, d in zip(trk_idx, det_idx):
            if dists[t, d] < self.distance_threshold:
                matched.append((d, t))
                unmatched_dets.remove(d)
                unmatched_trks.remove(t)
        return matched, unmatched_dets, unmatched_trks

    # 将新检测（new_detections）与暂挂的跟踪器（suspended_trackers）重新匹配
    def reid_match(self, new_detections):

        # 如果暂挂跟踪器或新检测为空，直接返回
        if len(self.suspended_trackers) == 0 or len(new_detections) == 0:
            return [], list(range(len(new_detections))), []

        # 获取暂挂跟踪器的预测位置
        old_preds = np.array([trk.kf.x[:3].flatten() for trk in self.suspended_trackers])
        
        # 计算距离矩阵（暂挂跟踪器 x 新检测）
        dists = np.linalg.norm(old_preds[:, None] - new_detections[:, :3], axis=2)
        
        # 匈牙利算法匹配
        trk_idx, det_idx = linear_sum_assignment(dists)

        matched = []
        unmatched_dets = list(range(len(new_detections)))
        for t, d in zip(trk_idx, det_idx):
            if dists[t, d] < self.distance_threshold * 2:
                matched.append((d, self.suspended_trackers[t]))
                unmatched_dets.remove(d)
        return matched, unmatched_dets, []

# ---------- 可视化 ----------
def visualize(frames, tracked_results, max_trace_length=100):
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')
    colors = {}
    trace_histories = {}
    true_tracks = {}

    for frame in frames:
        for pt in frame:
            if len(pt) < 5:
                continue
            tid = int(pt[4])
            if tid == -1:
                continue
            true_tracks.setdefault(tid, []).append(pt[:3])

    for frame_idx, (raw, tracked) in enumerate(zip(frames, tracked_results)):
        ax.clear()
        ax.set_title(f'Frame {frame_idx}')
        ax.set_xlim(-1000, 1000)
        ax.set_ylim(-1000, 1000)
        ax.set_zlim(-1000, 1000)

        if len(raw) > 0:
            ax.scatter(raw[:, 0], raw[:, 1], raw[:, 2], c='gray', marker='o', alpha=0.3, label='Raw')

        for tid, track in true_tracks.items():
            if len(track) < 2:
                continue
            track = np.array(track[:frame_idx + 1])
            if len(track) > 1:
                ax.plot(track[:, 0], track[:, 1], track[:, 2], color='red', linestyle='dashed', linewidth=2)
                ax.scatter(track[-1, 0], track[-1, 1], track[-1, 2], c='red', marker='o', s=30)

        for pt in tracked:
            tid = int(pt[3])
            if tid not in colors:
                colors[tid] = np.random.rand(3)
            if tid not in trace_histories:
                trace_histories[tid] = []
            trace_histories[tid].append((frame_idx, pt[:3]))
            trace_histories[tid] = [x for x in trace_histories[tid] if frame_idx - x[0] <= max_trace_length]
            trace = np.array([x[1] for x in trace_histories[tid]])
            if len(trace) > 1:
                ax.plot(trace[:, 0], trace[:, 1], trace[:, 2], color=colors[tid], linewidth=2)
            ax.scatter(pt[0], pt[1], pt[2], c=[colors[tid]], marker='x')
            ax.text(pt[0], pt[1], pt[2], f'ID{tid}', color=colors[tid], fontsize=8)

        plt.pause(0.05)

    plt.show()

# ---------- 单元测试入口 ----------
def unit_test():
    frames = generate_synthetic_data(num_frames=300, num_targets=5)
    tracker = PointTracker()
    tracked_history = []

    for frame in frames:
        clustered = cluster_detections(frame, eps=0.5, min_samples=1)
        tracked = tracker.update(clustered)
        tracked_history.append(tracked)

    visualize(frames, tracked_history)

if __name__ == '__main__':
    unit_test()
