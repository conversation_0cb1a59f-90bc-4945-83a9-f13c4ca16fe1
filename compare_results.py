#!/usr/bin/env python3
"""
比较Python版本和C++版本的跟踪结果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def load_csv_data(filename):
    """加载CSV数据"""
    try:
        df = pd.read_csv(filename)
        return df
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return None

def analyze_tracking_results(cpp_results, real_data):
    """分析跟踪结果"""
    print("=== 跟踪结果分析 ===")
    
    if cpp_results is not None:
        print(f"C++版本跟踪结果:")
        print(f"  总帧数: {cpp_results['frame'].max() + 1}")
        print(f"  跟踪目标数: {len(cpp_results['id'].unique())}")
        print(f"  总跟踪点数: {len(cpp_results)}")
        print(f"  平均每帧跟踪点数: {len(cpp_results) / (cpp_results['frame'].max() + 1):.2f}")
        
        # 分析每个ID的跟踪长度
        id_lengths = cpp_results.groupby('id').size()
        print(f"  跟踪长度统计:")
        print(f"    最短: {id_lengths.min()}")
        print(f"    最长: {id_lengths.max()}")
        print(f"    平均: {id_lengths.mean():.2f}")
    
    if real_data is not None:
        print(f"\n真实数据:")
        print(f"  总帧数: {real_data['frame'].max() + 1}")
        print(f"  真实目标数: {len(real_data[real_data['id'] != -1]['id'].unique())}")
        print(f"  总检测点数: {len(real_data)}")
        print(f"  杂波点数: {len(real_data[real_data['id'] == -1])}")

def visualize_comparison(cpp_results, real_data):
    """可视化比较结果"""
    fig = plt.figure(figsize=(15, 5))
    
    # 子图1: C++跟踪结果
    ax1 = fig.add_subplot(131, projection='3d')
    if cpp_results is not None:
        for track_id in cpp_results['id'].unique():
            track_data = cpp_results[cpp_results['id'] == track_id]
            ax1.plot(track_data['x'], track_data['y'], track_data['z'], 
                    marker='o', label=f'Track {track_id}')
    ax1.set_title('C++ Tracking Results')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 子图2: 真实轨迹
    ax2 = fig.add_subplot(132, projection='3d')
    if real_data is not None:
        # 绘制真实目标轨迹
        real_targets = real_data[real_data['id'] != -1]
        for target_id in real_targets['id'].unique():
            target_data = real_targets[real_targets['id'] == target_id]
            ax2.plot(target_data['x'], target_data['y'], target_data['z'], 
                    marker='s', label=f'Target {target_id}')
        
        # 绘制杂波点
        clutter = real_data[real_data['id'] == -1]
        if len(clutter) > 0:
            ax2.scatter(clutter['x'], clutter['y'], clutter['z'], 
                       c='gray', marker='.', alpha=0.3, label='Clutter')
    ax2.set_title('Ground Truth')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 子图3: 跟踪数量对比
    ax3 = fig.add_subplot(133)
    if cpp_results is not None and real_data is not None:
        # 统计每帧的跟踪数量
        cpp_counts = cpp_results.groupby('frame').size()
        real_counts = real_data[real_data['id'] != -1].groupby('frame').size()
        
        frames = range(max(cpp_counts.index.max(), real_counts.index.max()) + 1)
        cpp_frame_counts = [cpp_counts.get(f, 0) for f in frames]
        real_frame_counts = [real_counts.get(f, 0) for f in frames]
        
        ax3.plot(frames, cpp_frame_counts, 'b-', label='C++ Tracks', marker='o')
        ax3.plot(frames, real_frame_counts, 'r-', label='Real Targets', marker='s')
        ax3.set_xlabel('Frame')
        ax3.set_ylabel('Number of Tracks/Targets')
        ax3.set_title('Tracks per Frame')
        ax3.legend()
        ax3.grid(True)
    
    plt.tight_layout()
    plt.savefig('tracking_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()

def calculate_tracking_metrics(cpp_results, real_data):
    """计算跟踪性能指标"""
    if cpp_results is None or real_data is None:
        return
    
    print("\n=== 性能指标 ===")
    
    # 简单的距离匹配评估
    total_frames = max(cpp_results['frame'].max(), real_data['frame'].max()) + 1
    matched_frames = 0
    total_distance_error = 0
    
    for frame in range(total_frames):
        cpp_frame = cpp_results[cpp_results['frame'] == frame]
        real_frame = real_data[(real_data['frame'] == frame) & (real_data['id'] != -1)]
        
        if len(cpp_frame) > 0 and len(real_frame) > 0:
            # 简单的最近邻匹配
            for _, cpp_track in cpp_frame.iterrows():
                cpp_pos = np.array([cpp_track['x'], cpp_track['y'], cpp_track['z']])
                
                min_dist = float('inf')
                for _, real_target in real_frame.iterrows():
                    real_pos = np.array([real_target['x'], real_target['y'], real_target['z']])
                    dist = np.linalg.norm(cpp_pos - real_pos)
                    min_dist = min(min_dist, dist)
                
                if min_dist < 100:  # 距离阈值
                    total_distance_error += min_dist
                    matched_frames += 1
    
    if matched_frames > 0:
        avg_error = total_distance_error / matched_frames
        print(f"平均位置误差: {avg_error:.2f}")
        print(f"匹配的跟踪点数: {matched_frames}")
    else:
        print("没有找到匹配的跟踪点")

def main():
    print("比较Python版本和C++版本的跟踪结果")
    print("=" * 50)
    
    # 加载数据
    cpp_results = load_csv_data('output.csv')
    real_data = load_csv_data('real.csv')
    
    # 分析结果
    analyze_tracking_results(cpp_results, real_data)
    
    # 计算性能指标
    calculate_tracking_metrics(cpp_results, real_data)
    
    # 可视化比较
    print("\n生成可视化比较图...")
    visualize_comparison(cpp_results, real_data)
    
    print("\n分析完成！")

if __name__ == '__main__':
    main()
