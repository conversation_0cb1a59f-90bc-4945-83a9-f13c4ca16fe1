#pragma once
#include <vector>
#include <memory>
#include "KalmanFilter3D.hpp"
#include "Point.hpp"

struct TrackResult {
    Eigen::Vector3f position;
    int id;
};

class PointTracker {
public:
    PointTracker(int max_age = 5, int reid_age = 20, float distance_threshold = 50.0f, int min_hits = 2);

    std::vector<TrackResult> update(const std::vector<Point>& detections);

private:
    int max_age;
    int reid_age;
    float distance_threshold;
    int min_hits;
    int frame_count;

    std::vector<std::shared_ptr<KalmanFilter3D>> trackers;
    std::vector<std::shared_ptr<KalmanFilter3D>> suspended;

    void match(const std::vector<Eigen::Vector3f>& detections,
               const std::vector<Eigen::Vector3f>& predictions,
               std::vector<std::pair<int, int>>& matched,
               std::vector<int>& unmatched_dets,
               std::vector<int>& unmatched_trks);

    void reidMatch(const std::vector<Eigen::Vector3f>& detections,
                   std::vector<std::pair<int, std::shared_ptr<KalmanFilter3D>>>& reid_matched,
                   std::vector<int>& unmatched_dets);
};