#pragma once
#include <eigen3/Eigen/Dense>
#include <vector>

class KalmanFilter3D {
public:
    KalmanFilter3D(const Eigen::Vector3f& init_pos);

    void predict();
    void update(const Eigen::Vector3f& measurement);
    Eigen::Vector3f getPrediction() const;
    
    void assignId();        // 延迟分配ID
    int getId() const;      // 获取ID
    bool hasId() const;     // 是否已分配ID
    
    int getAge() const;
    int getTimeSinceUpdate() const;
    int getHitStreak() const;

    void reset(const Eigen::Vector3f& new_pos);

    // void increaseTime();
    // void setLastUpdateFrame(int frame);
    // int getLastUpdateFrame() const;
    
    std::vector<Eigen::Vector3f> getHistory() const;

private:
    static int next_id;
    int id;
    bool id_assigned;

    int age;
    int time_since_update;
    int hit_streak;

    std::vector<Eigen::Vector3f> history;

    Eigen::Matrix<float, 6, 1> x;   // 状态向量: x, y, z, vx, vy, vz
    Eigen::Matrix<float, 6, 6> F;   // 状态转移矩阵
    Eigen::Matrix<float, 3, 6> H;   // 观测矩阵
    Eigen::Matrix<float, 6, 6> P;   // 状态协方差
    Eigen::Matrix<float, 3, 3> R;   // 观测协方差
    Eigen::Matrix<float, 6, 6> Q;   // 过程噪声
};
