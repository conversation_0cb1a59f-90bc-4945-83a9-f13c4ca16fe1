#ifndef UTILS_HPP
#define UTILS_HPP

#include <vector>
#include <string>
#include <eigen3/Eigen/Dense>
#include "PointTracker.hpp"  // 包含 TrackedObject 结构定义

void writeResultsToCSV(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename);
void writeResultsToPLY(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename);

void writePointsToCSV(const std::vector<std::vector<Point>>& points, const std::string& filename);

// 简单的聚类函数，类似Python版本的cluster_detections
std::vector<Point> clusterDetections(const std::vector<Point>& points, float eps = 30.0f);

#endif  // UTILS_HPP
